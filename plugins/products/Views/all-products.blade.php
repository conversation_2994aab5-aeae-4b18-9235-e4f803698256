@extends('layouts.app')

@section('title', 'All Products')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">All Products</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Browse all available products in the system</p>
            </div>
            @if(auth()->user()->hasPermission('manage_products'))
                <a href="{{ route('products.create') }}" 
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-plus mr-2"></i>Add Product
                </a>
            @endif
        </div>

        @if($products->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($products as $product)
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-lg transition-shadow transition duration-150 ease-in-out">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center">
                                @if($product->icon)
                                    <i class="{{ $product->icon }} text-2xl text-blue-500 mr-3"></i>
                                @else
                                    <i class="fas fa-box text-2xl text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-3"></i>
                                @endif
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $product->name }}</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $product->slug }}</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $product->is_active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' }} transition duration-150 ease-in-out">
                                {{ $product->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>

                        @if($product->description)
                            <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4 line-clamp-3">{{ $product->description }}</p>
                        @endif

                        @if($product->target_audience)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Target Audience</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 line-clamp-2">{{ $product->target_audience }}</p>
                            </div>
                        @endif

                        @if($product->use_cases)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Use Cases</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 line-clamp-2">{{ $product->use_cases }}</p>
                            </div>
                        @endif

                        <!-- Pricing Preview -->
                        @if($product->pricingItems && $product->pricingItems->count() > 0)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Pricing</h4>
                                <div class="space-y-2">
                                    @foreach($product->pricingItems->take(2) as $pricing)
                                        <div class="flex justify-between items-center text-sm">
                                            <span class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $pricing->name }}</span>
                                            <span class="font-medium text-gray-900 dark:text-white">{{ $pricing->formatted_price }}</span>
                                        </div>
                                    @endforeach
                                    @if($product->pricingItems->count() > 2)
                                        <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">+{{ $product->pricingItems->count() - 2 }} more pricing options</p>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Reference Links -->
                        @if($product->reference_links && count($product->reference_links) > 0)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Resources</h4>
                                <div class="space-y-1">
                                    @foreach(array_slice($product->reference_links, 0, 2) as $link)
                                        <a href="{{ $link }}" target="_blank" 
                                           class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 block truncate">
                                            {{ parse_url($link, PHP_URL_HOST) }} <i class="fas fa-external-link-alt ml-1"></i>
                                        </a>
                                    @endforeach
                                    @if(count($product->reference_links) > 2)
                                        <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">+{{ count($product->reference_links) - 2 }} more links</p>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Statistics -->
                        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-4">
                            <span><i class="fas fa-building mr-1"></i>{{ $product->businesses_count }} businesses</span>
                            @if($product->latest_release)
                                <span><i class="fas fa-tag mr-1"></i>{{ $product->latest_release->formatted_version }}</span>
                            @endif
                        </div>

                        <!-- Actions -->
                        <div class="flex justify-between items-center">
                            <a href="{{ route('products.show', $product) }}" 
                               class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 text-sm font-medium">
                                View Details
                            </a>
                            <div class="flex space-x-2">
                                @if(auth()->user()->hasPermission('view_products'))
                                    <a href="{{ route('products.show', $product) }}" 
                                       class="bg-blue-100 dark:bg-blue-900 hover:bg-blue-200 text-blue-800 dark:text-blue-200 text-xs font-medium py-1 px-2 rounded transition duration-150 ease-in-out">
                                        View
                                    </a>
                                @endif
                                @if(auth()->user()->hasPermission('manage_products'))
                                    <a href="{{ route('products.edit', $product) }}" 
                                       class="bg-indigo-100 hover:bg-indigo-200 text-indigo-800 text-xs font-medium py-1 px-2 rounded transition duration-150 ease-in-out">
                                        Edit
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-box text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No products available</h3>
                <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-6">There are no products in the system yet.</p>
                @if(auth()->user()->hasPermission('manage_products'))
                    <a href="{{ route('products.create') }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-plus mr-2"></i>Create First Product
                    </a>
                @endif
            </div>
        @endif
    </div>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection
