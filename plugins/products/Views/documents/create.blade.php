@extends('layouts.app')

@section('title', 'Upload Document - ' . $product->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Upload Document</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">{{ $product->name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('products.documents.index', $product) }}" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Documents
                </a>
                <a href="{{ route('products.show', $product) }}" 
                   class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Product
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <form method="POST" action="{{ route('products.documents.store', $product) }}" enctype="multipart/form-data" class="p-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Document Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Document Information</h3>
                    </div>

                    <!-- Document Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Document Name <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('name') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">A descriptive name for this document</p>
                    </div>

                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Category <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <select name="category" id="category" required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('category') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                            <option value="">Select a category...</option>
                            @foreach($categories as $categoryKey => $categoryLabel)
                                <option value="{{ $categoryKey }}" {{ old('category') === $categoryKey ? 'selected' : '' }}>
                                    {{ $categoryLabel }}
                                </option>
                            @endforeach
                        </select>
                        @error('category')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Choose the appropriate document category</p>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                        <textarea name="description" id="description" rows="3"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('description') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Optional description of the document content</p>
                    </div>

                    <!-- Version -->
                    <div>
                        <label for="version" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Version</label>
                        <input type="text" name="version" id="version" value="{{ old('version', '1.0') }}"
                               placeholder="1.0"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('version') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        @error('version')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Document version (e.g., 1.0, 2.1, etc.)</p>
                    </div>

                    <!-- File Upload -->
                    <div class="md:col-span-2">
                        <label for="file" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Document File <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                    <label for="file" class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500 transition duration-150 ease-in-out">
                                        <span>Upload a file</span>
                                        <input id="file" name="file" type="file" class="sr-only" required accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.zip,.rar">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                    PDF, DOC, XLS, PPT, TXT, Images, ZIP up to 10MB
                                </p>
                            </div>
                        </div>
                        @error('file')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        
                        <!-- File Preview -->
                        <div id="file-preview" class="mt-4 hidden">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-md p-4 transition duration-150 ease-in-out">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i id="file-icon" class="fas fa-file text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-2xl"></i>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <p id="file-name" class="text-sm font-medium text-gray-900 dark:text-white"></p>
                                        <p id="file-size" class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"></p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <button type="button" onclick="clearFile()" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('products.documents.index', $product) }}" 
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-upload mr-2"></i>Upload Document
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.getElementById('file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        showFilePreview(file);
    }
});

function showFilePreview(file) {
    const preview = document.getElementById('file-preview');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const fileIcon = document.getElementById('file-icon');
    
    // Set file name
    fileName.textContent = file.name;
    
    // Set file size
    const size = formatFileSize(file.size);
    fileSize.textContent = size;
    
    // Set file icon based on extension
    const extension = file.name.split('.').pop().toLowerCase();
    const iconClass = getFileIcon(extension);
    fileIcon.className = iconClass;
    
    // Show preview
    preview.classList.remove('hidden');
    
    // Auto-fill document name if empty
    const nameField = document.getElementById('name');
    if (!nameField.value) {
        const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, "");
        nameField.value = nameWithoutExtension;
    }
}

function clearFile() {
    document.getElementById('file').value = '';
    document.getElementById('file-preview').classList.add('hidden');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileIcon(extension) {
    const iconMap = {
        'pdf': 'fas fa-file-pdf text-red-500 dark:text-red-400 text-2xl',
        'doc': 'fas fa-file-word text-blue-500 text-2xl',
        'docx': 'fas fa-file-word text-blue-500 text-2xl',
        'xls': 'fas fa-file-excel text-green-500 text-2xl',
        'xlsx': 'fas fa-file-excel text-green-500 text-2xl',
        'ppt': 'fas fa-file-powerpoint text-orange-500 text-2xl',
        'pptx': 'fas fa-file-powerpoint text-orange-500 text-2xl',
        'txt': 'fas fa-file-alt text-gray-500 dark:text-gray-400 dark:text-gray-500 text-2xl',
        'jpg': 'fas fa-file-image text-purple-500 text-2xl',
        'jpeg': 'fas fa-file-image text-purple-500 text-2xl',
        'png': 'fas fa-file-image text-purple-500 text-2xl',
        'gif': 'fas fa-file-image text-purple-500 text-2xl',
        'zip': 'fas fa-file-archive text-yellow-500 text-2xl',
        'rar': 'fas fa-file-archive text-yellow-500 text-2xl',
    };
    
    return iconMap[extension] || 'fas fa-file text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-2xl';
}

// Drag and drop functionality
const dropZone = document.querySelector('.border-dashed');

dropZone.addEventListener('dragover', function(e) {
    e.preventDefault();
    dropZone.classList.add('border-blue-500', 'bg-blue-50');
});

dropZone.addEventListener('dragleave', function(e) {
    e.preventDefault();
    dropZone.classList.remove('border-blue-500', 'bg-blue-50');
});

dropZone.addEventListener('drop', function(e) {
    e.preventDefault();
    dropZone.classList.remove('border-blue-500', 'bg-blue-50');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        document.getElementById('file').files = files;
        showFilePreview(files[0]);
    }
});
</script>
@endsection
