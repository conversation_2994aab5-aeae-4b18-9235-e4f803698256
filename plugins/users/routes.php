<?php

use Illuminate\Support\Facades\Route;
use Plugins\Users\Controllers\UserController;
use Plugins\Users\Controllers\RoleController;
use Plugins\Users\Controllers\PermissionController;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;

// Explicit route model bindings
Route::bind('user', function ($value) {
    return User::findOrFail($value);
});

Route::bind('role', function ($value) {
    return Role::findOrFail($value);
});

Route::bind('permission', function ($value) {
    return Permission::findOrFail($value);
});

// User Management Routes
Route::resource('users', UserController::class);
Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

// Role Management Routes
Route::resource('roles', RoleController::class);

// Permission Management Routes
Route::resource('permissions', PermissionController::class);
