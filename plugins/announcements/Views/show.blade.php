@extends('layouts.app')

@section('title', $announcement->title)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $announcement->title }}</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Announcement Details</p>
            </div>
            <div class="flex space-x-3">
                @if(auth()->user()->hasPermission('manage_announcements'))
                    <a href="{{ route('announcements.edit', $announcement) }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                @endif
                <a href="{{ route('announcements.index') }}"
                   class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Announcements
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Announcement Content -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg mb-6 transition duration-150 ease-in-out">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $announcement->getPriorityBadgeClass() }}">
                                    {{ ucfirst($announcement->priority) }}
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $announcement->getTypeBadgeClass() }}">
                                    {{ ucfirst($announcement->type) }}
                                </span>
                                @if($announcement->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition duration-150 ease-in-out">
                                        Inactive
                                    </span>
                                @endif
                                @if($announcement->requires_acknowledgment)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 transition duration-150 ease-in-out">
                                        Requires Acknowledgment
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="px-6 py-4">
                        <div class="prose max-w-none dark:prose-invert text-gray-900 dark:text-white">
                            {!! nl2br(e($announcement->content)) !!}
                        </div>
                    </div>
                </div>

                <!-- Read Activity -->
                @if($recentReads->count() > 0)
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Recent Read Activity</h3>
                        </div>
                        <div class="px-6 py-4">
                            <div class="space-y-3">
                                @foreach($recentReads as $read)
                                    <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <div class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center transition duration-150 ease-in-out">
                                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                        {{ substr($read->user->name, 0, 1) }}
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $read->user->name }}</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ $read->user->email }}</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm text-gray-900 dark:text-white">
                                                Read {{ $read->read_at->diffForHumans() }}
                                            </p>
                                            @if($read->acknowledged_at)
                                                <p class="text-xs text-green-600 dark:text-green-400">
                                                    Acknowledged {{ $read->acknowledged_at->diffForHumans() }}
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Announcement Info -->
                <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg mb-6 transition duration-150 ease-in-out">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Announcement Info</h3>
                    </div>
                    <div class="px-6 py-4 space-y-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created by</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $announcement->creator->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created at</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $announcement->created_at->format('M j, Y g:i A') }}</dd>
                        </div>
                        @if($announcement->published_at)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Published at</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $announcement->published_at->format('M j, Y g:i A') }}</dd>
                            </div>
                        @endif
                        @if($announcement->expires_at)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Expires at</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $announcement->expires_at->format('M j, Y g:i A') }}</dd>
                            </div>
                        @endif
                        @if($announcement->target_roles && count($announcement->target_roles) > 0)
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Target roles</dt>
                                <dd class="mt-1">
                                    @foreach($announcement->target_roles as $role)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 mr-1 mb-1 transition duration-150 ease-in-out">
                                            {{ ucfirst($role) }}
                                        </span>
                                    @endforeach
                                </dd>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Read Statistics (Only visible to announcement creator) -->
                @if(auth()->id() === $announcement->created_by)
                    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Read Statistics</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Only visible to the announcement creator</p>
                        </div>
                        <div class="px-6 py-4 space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Total users</dt>
                                <dd class="mt-1 text-2xl font-semibold text-gray-900 dark:text-white">{{ $readStats['total_users'] }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Users who read</dt>
                                <dd class="mt-1 text-2xl font-semibold text-blue-600 dark:text-blue-400">
                                    {{ $readStats['read_count'] }}
                                    <span class="text-sm font-normal text-gray-500 dark:text-gray-400">({{ $readStats['read_percentage'] }}%)</span>
                                </dd>
                            </div>
                            @if($announcement->requires_acknowledgment)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Users who acknowledged</dt>
                                    <dd class="mt-1 text-2xl font-semibold text-green-600 dark:text-green-400">
                                        {{ $readStats['acknowledged_count'] }}
                                        <span class="text-sm font-normal text-gray-500 dark:text-gray-400">({{ $readStats['acknowledged_percentage'] }}%)</span>
                                    </dd>
                                </div>
                                @if($readStats['pending_acknowledgments'] > 0)
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending acknowledgments</dt>
                                        <dd class="mt-1 text-2xl font-semibold text-orange-600 dark:text-orange-400">
                                            {{ $readStats['pending_acknowledgments'] }}
                                            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">users still need to acknowledge</span>
                                        </dd>
                                    </div>
                                @endif
                            @endif

                            <!-- Progress Bars -->
                            <div class="space-y-3">
                                <div>
                                    <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                        <span>Read Progress</span>
                                        <span>{{ $readStats['read_percentage'] }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 transition duration-150 ease-in-out">
                                        <div class="bg-blue-600 h-2 rounded-full transition duration-150 ease-in-out" style="width: {{ $readStats['read_percentage'] }}%"></div>
                                    </div>
                                </div>
                                @if($announcement->requires_acknowledgment)
                                    <div>
                                        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                            <span>Acknowledgment Progress</span>
                                            <span>{{ $readStats['acknowledged_percentage'] }}%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 transition duration-150 ease-in-out">
                                            <div class="bg-green-600 h-2 rounded-full transition duration-150 ease-in-out" style="width: {{ $readStats['acknowledged_percentage'] }}%"></div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
