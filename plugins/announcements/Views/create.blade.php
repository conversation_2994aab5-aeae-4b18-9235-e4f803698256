@extends('layouts.app')

@section('title', 'Create Announcement')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Create New Announcement</h1>
            <a href="{{ route('announcements.index') }}"
               class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-7000 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Back to Announcements
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg border border-gray-200 dark:border-gray-700 transition duration-150 ease-in-out">
            <form method="POST" action="{{ route('announcements.store') }}" class="p-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h3>
                    </div>

                    <!-- Title -->
                    <div class="md:col-span-2">
                        <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Title <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <input type="text" name="title" id="title" value="{{ old('title') }}" required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 @error('title') border-red-300 dark:border-red-600 @enderror transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">A clear and concise title for the announcement</p>
                    </div>

                    <!-- Content -->
                    <div class="md:col-span-2">
                        <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Content <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <textarea name="content" id="content" rows="6" required
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 @error('content') border-red-300 dark:border-red-600 @enderror transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">{{ old('content') }}</textarea>
                        @error('content')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">The main content of the announcement. You can use HTML formatting.</p>
                    </div>

                    <!-- Priority -->
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Priority <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <select name="priority" id="priority" required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 @error('priority') border-red-300 dark:border-red-600 @enderror transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                            <option value="">Select Priority</option>
                            <option value="low" {{ old('priority') === 'low' ? 'selected' : '' }}>Low</option>
                            <option value="normal" {{ old('priority') === 'normal' ? 'selected' : '' }}>Normal</option>
                            <option value="high" {{ old('priority') === 'high' ? 'selected' : '' }}>High</option>
                            <option value="urgent" {{ old('priority') === 'urgent' ? 'selected' : '' }}>Urgent</option>
                        </select>
                        @error('priority')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Higher priority announcements are displayed more prominently</p>
                    </div>

                    <!-- Type -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Type <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <select name="type" id="type" required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 @error('type') border-red-300 dark:border-red-600 @enderror transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                            <option value="">Select Type</option>
                            <option value="info" {{ old('type') === 'info' ? 'selected' : '' }}>Info</option>
                            <option value="success" {{ old('type') === 'success' ? 'selected' : '' }}>Success</option>
                            <option value="warning" {{ old('type') === 'warning' ? 'selected' : '' }}>Warning</option>
                            <option value="error" {{ old('type') === 'error' ? 'selected' : '' }}>Error</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">The type affects the visual styling of the announcement</p>
                    </div>

                    <!-- Publishing Settings -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Publishing Settings</h3>
                    </div>

                    <!-- Published At -->
                    <div>
                        <label for="published_at" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Publish Date & Time
                        </label>
                        <input type="datetime-local" name="published_at" id="published_at" 
                               value="{{ old('published_at', now()->format('Y-m-d\TH:i')) }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('published_at') border-red-300 dark:border-red-600 @enderror">
                        @error('published_at')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500">When the announcement should become visible to users</p>
                    </div>

                    <!-- Expires At -->
                    <div>
                        <label for="expires_at" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Expiry Date & Time
                        </label>
                        <input type="datetime-local" name="expires_at" id="expires_at" value="{{ old('expires_at') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('expires_at') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        @error('expires_at')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500">Optional: When the announcement should stop being visible (leave empty for no expiry)</p>
                    </div>

                    <!-- Target Roles -->
                    <div class="md:col-span-2">
                        <label for="target_roles" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Target Roles
                        </label>
                        <div class="mt-2 space-y-2">
                            @php
                                $roles = \App\Models\Role::all();
                                $selectedRoles = old('target_roles', []);
                            @endphp
                            @foreach($roles as $role)
                                <label class="inline-flex items-center mr-6">
                                    <input type="checkbox" name="target_roles[]" value="{{ $role->name }}" 
                                           {{ in_array($role->name, $selectedRoles) ? 'checked' : '' }}
                                           class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ $role->display_name ?? ucfirst($role->name) }}</span>
                                </label>
                            @endforeach
                        </div>
                        @error('target_roles')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500">Leave empty to show to all users, or select specific roles</p>
                    </div>

                    <!-- Options -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Options</h3>
                    </div>

                    <!-- Is Active -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" 
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white">
                            <label for="is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Active
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500">Whether the announcement is active and can be displayed</p>
                    </div>

                    <!-- Requires Acknowledgment -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="requires_acknowledgment" id="requires_acknowledgment" value="1" 
                                   {{ old('requires_acknowledgment', true) ? 'checked' : '' }}
                                   class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white">
                            <label for="requires_acknowledgment" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Requires Acknowledgment
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500">Whether users must explicitly acknowledge reading this announcement</p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('announcements.index') }}" 
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Create Announcement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-adjust textarea height
    const contentTextarea = document.getElementById('content');
    contentTextarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });

    // Validate expiry date is after publish date
    const publishedAt = document.getElementById('published_at');
    const expiresAt = document.getElementById('expires_at');
    
    function validateDates() {
        if (publishedAt.value && expiresAt.value) {
            const publishDate = new Date(publishedAt.value);
            const expiryDate = new Date(expiresAt.value);
            
            if (expiryDate <= publishDate) {
                expiresAt.setCustomValidity('Expiry date must be after publish date');
            } else {
                expiresAt.setCustomValidity('');
            }
        } else {
            expiresAt.setCustomValidity('');
        }
    }
    
    publishedAt.addEventListener('change', validateDates);
    expiresAt.addEventListener('change', validateDates);
});
</script>
@endsection
