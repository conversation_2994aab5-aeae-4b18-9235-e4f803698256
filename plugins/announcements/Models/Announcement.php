<?php

namespace Plugins\Announcements\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;

class Announcement extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'content',
        'priority',
        'type',
        'is_active',
        'requires_acknowledgment',
        'published_at',
        'expires_at',
        'target_roles',
        'metadata',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'requires_acknowledgment' => 'boolean',
        'published_at' => 'datetime',
        'expires_at' => 'datetime',
        'target_roles' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the user who created this announcement
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all read records for this announcement
     */
    public function reads()
    {
        return $this->hasMany(AnnouncementRead::class);
    }

    /**
     * Get users who have read this announcement
     */
    public function readByUsers()
    {
        return $this->belongsToMany(User::class, 'announcement_reads')
                    ->withPivot(['read_at', 'acknowledged_at', 'ip_address', 'user_agent'])
                    ->withTimestamps();
    }

    /**
     * Get users who have acknowledged this announcement
     */
    public function acknowledgedByUsers()
    {
        return $this->belongsToMany(User::class, 'announcement_reads')
                    ->wherePivotNotNull('acknowledged_at')
                    ->withPivot(['read_at', 'acknowledged_at', 'ip_address', 'user_agent'])
                    ->withTimestamps();
    }

    /**
     * Scope to get only active announcements
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only published announcements
     */
    public function scopePublished($query)
    {
        return $query->where('published_at', '<=', now())
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope to get announcements by priority
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get announcements by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get announcements requiring acknowledgment
     */
    public function scopeRequiringAcknowledgment($query)
    {
        return $query->where('requires_acknowledgment', true);
    }

    /**
     * Check if announcement is visible to a specific user
     */
    public function isVisibleToUser(User $user): bool
    {
        // Check if announcement is active and published
        if (!$this->is_active || !$this->isPublished()) {
            return false;
        }

        // Check target roles
        if (!empty($this->target_roles)) {
            $userRoles = [$user->role->name ?? ''];
            if ($user->roles) {
                $userRoles = array_merge($userRoles, $user->roles->pluck('name')->toArray());
            }
            
            return !empty(array_intersect($this->target_roles, $userRoles));
        }

        return true;
    }

    /**
     * Check if announcement is published
     */
    public function isPublished(): bool
    {
        if (!$this->published_at) {
            return false;
        }

        $now = now();
        $isAfterPublishDate = $this->published_at <= $now;
        $isBeforeExpiry = !$this->expires_at || $this->expires_at > $now;

        return $isAfterPublishDate && $isBeforeExpiry;
    }

    /**
     * Check if user has read this announcement
     */
    public function isReadByUser(User $user): bool
    {
        return $this->reads()->where('user_id', $user->id)->whereNotNull('read_at')->exists();
    }

    /**
     * Check if user has acknowledged this announcement
     */
    public function isAcknowledgedByUser(User $user): bool
    {
        return $this->reads()->where('user_id', $user->id)->whereNotNull('acknowledged_at')->exists();
    }

    /**
     * Mark as read by user
     */
    public function markAsReadByUser(User $user, array $metadata = []): AnnouncementRead
    {
        return AnnouncementRead::updateOrCreate(
            [
                'announcement_id' => $this->id,
                'user_id' => $user->id,
            ],
            [
                'read_at' => now(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'metadata' => $metadata,
            ]
        );
    }

    /**
     * Mark as acknowledged by user
     */
    public function acknowledgeByUser(User $user, array $metadata = []): AnnouncementRead
    {
        $read = $this->markAsReadByUser($user, $metadata);
        $read->update(['acknowledged_at' => now()]);
        
        return $read;
    }

    /**
     * Get read statistics
     */
    public function getReadStats(): array
    {
        $totalUsers = User::active()->count();
        $readCount = $this->reads()->whereNotNull('read_at')->count();
        $acknowledgedCount = $this->reads()->whereNotNull('acknowledged_at')->count();
        $pendingAcknowledgments = $this->requires_acknowledgment ? ($totalUsers - $acknowledgedCount) : 0;

        return [
            'total_users' => $totalUsers,
            'read_count' => $readCount,
            'acknowledged_count' => $acknowledgedCount,
            'pending_acknowledgments' => $pendingAcknowledgments,
            'read_percentage' => $totalUsers > 0 ? round(($readCount / $totalUsers) * 100, 2) : 0,
            'acknowledged_percentage' => $totalUsers > 0 ? round(($acknowledgedCount / $totalUsers) * 100, 2) : 0,
        ];
    }

    /**
     * Get pending acknowledgment count for announcements requiring acknowledgment
     */
    public function getPendingAcknowledgmentCount(): int
    {
        if (!$this->requires_acknowledgment) {
            return 0;
        }

        $totalUsers = User::active()->count();
        $acknowledgedCount = $this->reads()->whereNotNull('acknowledged_at')->count();

        return max(0, $totalUsers - $acknowledgedCount);
    }

    /**
     * Get priority badge class for UI
     */
    public function getPriorityBadgeClass(): string
    {
        return match($this->priority) {
            'urgent' => 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
            'high' => 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200',
            'normal' => 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
            'low' => 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
            default => 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',
        };
    }

    /**
     * Get type badge class for UI
     */
    public function getTypeBadgeClass(): string
    {
        return match($this->type) {
            'error' => 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
            'warning' => 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
            'success' => 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
            'info' => 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
            default => 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
        };
    }
}
