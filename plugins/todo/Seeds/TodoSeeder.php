<?php

namespace Plugins\Todo\Seeds;

use Illuminate\Database\Seeder;

class TodoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Seeding Todo plugin data...');
        
        // Run permissions seeder first
        $this->call(TodoPermissionsSeeder::class);
        
        // Then run sample data seeder
        $this->call(TodoSampleDataSeeder::class);
        
        $this->command->info('Todo plugin seeding completed!');
    }
}
