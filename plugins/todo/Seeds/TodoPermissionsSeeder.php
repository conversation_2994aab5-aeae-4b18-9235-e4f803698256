<?php

namespace Plugins\Todo\Seeds;

use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class TodoPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            [
                'name' => 'manage_todos',
                'display_name' => 'Manage Todos',
                'description' => 'Create, edit, delete, and reorder todo items',
                'category' => 'Todo Management'
            ],
            [
                'name' => 'view_todos',
                'display_name' => 'View Todos',
                'description' => 'View and access todo list',
                'category' => 'Todo Management'
            ],
        ];

        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                $permissionData
            );
        }

        // Assign permissions to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $todoPermissions = Permission::whereIn('name', ['manage_todos', 'view_todos'])->get();
            foreach ($todoPermissions as $permission) {
                if (!$adminRole->permissions->contains($permission)) {
                    $adminRole->permissions()->attach($permission);
                }
            }
        }

        // Assign view permission to user role
        $userRole = Role::where('name', 'user')->first();
        if ($userRole) {
            $viewPermission = Permission::where('name', 'view_todos')->first();
            if ($viewPermission && !$userRole->permissions->contains($viewPermission)) {
                $userRole->permissions()->attach($viewPermission);
            }
        }

        $this->command->info('Todo permissions created and assigned successfully!');
    }
}
