@extends('layouts.app')

@section('title', 'System Information')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">System Information</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-2">Detailed system configuration and status</p>
            </div>
            <div class="flex space-x-3">
                <button onclick="performHealthCheck()" class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-heartbeat mr-2"></i>Health Check
                </button>
                <a href="{{ route('settings.system.phpinfo') }}" target="_blank" class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-code mr-2"></i>PHP Info
                </a>
            </div>
        </div>
    </div>

    <!-- Health Status Alert -->
    <div id="healthStatus" class="hidden mb-6"></div>

    <!-- Application Information -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 transition duration-150 ease-in-out">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Application Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                @foreach($systemInfo['application'] as $key => $value)
                <div class="flex justify-between py-2 border-b border-gray-100">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
                    <span class="text-gray-900 dark:text-white">
                        @if($key === 'debug_mode')
                            <span class="px-2 py-1 text-xs rounded-full {{ $value === 'Enabled' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' }} transition duration-150 ease-in-out">
                                {{ $value }}
                            </span>
                        @else
                            {{ $value }}
                        @endif
                    </span>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Server Information -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 transition duration-150 ease-in-out">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Server Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                @foreach($systemInfo['server'] as $key => $value)
                <div class="flex justify-between py-2 border-b border-gray-100">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
                    <span class="text-gray-900 dark:text-white">{{ $value }}</span>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Database Information -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 transition duration-150 ease-in-out">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Database Information</h3>
        </div>
        <div class="p-6">
            @if(isset($systemInfo['database']['error']))
                <div class="bg-red-50 border border-red-200 rounded-md p-4 transition duration-150 ease-in-out">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-red-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="text-red-800 dark:text-red-200 font-medium">Database Error</h4>
                            <p class="text-red-700 mt-1">{{ $systemInfo['database']['error'] }}</p>
                        </div>
                    </div>
                </div>
            @else
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($systemInfo['database'] as $key => $value)
                    <div class="flex justify-between py-2 border-b border-gray-100">
                        <span class="font-medium text-gray-700 dark:text-gray-300">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
                        <span class="text-gray-900 dark:text-white">{{ $value }}</span>
                    </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>

    <!-- Storage Information -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 transition duration-150 ease-in-out">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Storage Information</h3>
        </div>
        <div class="p-6">
            @if(isset($systemInfo['storage']['error']))
                <div class="bg-red-50 border border-red-200 rounded-md p-4 transition duration-150 ease-in-out">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-red-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="text-red-800 dark:text-red-200 font-medium">Storage Error</h4>
                            <p class="text-red-700 mt-1">{{ $systemInfo['storage']['error'] }}</p>
                        </div>
                    </div>
                </div>
            @else
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($systemInfo['storage'] as $key => $value)
                    <div class="flex justify-between py-2 border-b border-gray-100">
                        <span class="font-medium text-gray-700 dark:text-gray-300">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
                        <span class="text-gray-900 dark:text-white">
                            @if(str_contains($key, 'writable'))
                                <span class="px-2 py-1 text-xs rounded-full {{ $value ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' }} transition duration-150 ease-in-out">
                                    {{ $value ? 'Yes' : 'No' }}
                                </span>
                            @else
                                {{ $value }}
                            @endif
                        </span>
                    </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>

    <!-- Performance Information -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition duration-150 ease-in-out">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Performance Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                @foreach($systemInfo['performance'] as $key => $value)
                <div class="flex justify-between py-2 border-b border-gray-100">
                    <span class="font-medium text-gray-700 dark:text-gray-300">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
                    <span class="text-gray-900 dark:text-white">{{ $value }}</span>
                </div>
                @endforeach
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50 transition duration-150 ease-in-out">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm mx-auto transition duration-150 ease-in-out">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
            <span>Performing health check...</span>
        </div>
    </div>
</div>

<script>
function performHealthCheck() {
    document.getElementById('loadingModal').classList.remove('hidden');
    document.getElementById('loadingModal').classList.add('flex');
    
    fetch('{{ route('settings.system.health-check') }}')
        .then(response => response.json())
        .then(data => {
            document.getElementById('loadingModal').classList.add('hidden');
            document.getElementById('loadingModal').classList.remove('flex');
            
            const healthStatus = document.getElementById('healthStatus');
            healthStatus.classList.remove('hidden');
            
            if (data.success) {
                const isHealthy = data.overall_health === 'healthy';
                healthStatus.className = `mb-6 p-4 rounded-md border ${isHealthy ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'}`;
                
                let html = `
                    <div class="flex">
                        <i class="fas ${isHealthy ? 'fa-check-circle text-green-400' : 'fa-exclamation-triangle text-yellow-400'} mr-3 mt-1"></i>
                        <div class="flex-1">
                            <h4 class="${isHealthy ? 'text-green-800 dark:text-green-200' : 'text-yellow-800 dark:text-yellow-200'} font-medium">
                                System Health: ${data.overall_health}
                            </h4>
                            <div class="mt-3 space-y-2">
                `;
                
                Object.entries(data.checks).forEach(([check, result]) => {
                    const statusColor = result.status === 'healthy' ? 'text-green-600 dark:text-green-400' : 
                                       result.status === 'warning' ? 'text-yellow-600' : 'text-red-600 dark:text-red-400';
                    const statusIcon = result.status === 'healthy' ? 'fa-check' : 
                                      result.status === 'warning' ? 'fa-exclamation-triangle' : 'fa-times';
                    
                    html += `
                        <div class="flex items-center text-sm">
                            <i class="fas ${statusIcon} ${statusColor} mr-2"></i>
                            <span class="font-medium capitalize">${check}:</span>
                            <span class="ml-2">${result.message}</span>
                        </div>
                    `;
                });
                
                html += `
                            </div>
                            <p class="text-xs ${isHealthy ? 'text-green-600 dark:text-green-400' : 'text-yellow-600'} mt-2">
                                Last checked: ${new Date(data.timestamp).toLocaleString()}
                            </p>
                        </div>
                    </div>
                `;
                
                healthStatus.innerHTML = html;
            } else {
                healthStatus.className = 'mb-6 p-4 rounded-md border bg-red-50 border-red-200';
                healthStatus.innerHTML = `
                    <div class="flex">
                        <i class="fas fa-times-circle text-red-400 mr-3 mt-1"></i>
                        <div>
                            <h4 class="text-red-800 dark:text-red-200 font-medium">Health Check Failed</h4>
                            <p class="text-red-700 mt-1">${data.message}</p>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            document.getElementById('loadingModal').classList.add('hidden');
            document.getElementById('loadingModal').classList.remove('flex');
            
            const healthStatus = document.getElementById('healthStatus');
            healthStatus.classList.remove('hidden');
            healthStatus.className = 'mb-6 p-4 rounded-md border bg-red-50 border-red-200';
            healthStatus.innerHTML = `
                <div class="flex">
                    <i class="fas fa-times-circle text-red-400 mr-3 mt-1"></i>
                    <div>
                        <h4 class="text-red-800 dark:text-red-200 font-medium">Health Check Error</h4>
                        <p class="text-red-700 mt-1">${error.message}</p>
                    </div>
                </div>
            `;
        });
}
</script>
@endsection
