<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            
            // Theme preferences
            $table->enum('theme', ['light', 'dark', 'system'])->default('light');
            
            // Language preferences
            $table->string('language', 10)->default('en');
            
            // UI preferences
            $table->boolean('sidebar_collapsed')->default(false);
            $table->enum('sidebar_position', ['left', 'right'])->default('left');
            
            // Notification preferences
            $table->boolean('email_notifications')->default(true);
            $table->boolean('browser_notifications')->default(true);
            $table->boolean('sound_notifications')->default(false);
            
            // Display preferences
            $table->enum('date_format', ['Y-m-d', 'm/d/Y', 'd/m/Y', 'd-m-Y'])->default('Y-m-d');
            $table->enum('time_format', ['24', '12'])->default('24');
            $table->string('timezone')->default('UTC');
            
            // Additional preferences (JSON for extensibility)
            $table->json('additional_preferences')->nullable();
            
            $table->timestamps();
            
            // Unique constraint - one preference record per user
            $table->unique(['user_id']);
            
            // Indexes
            $table->index(['user_id']);
            $table->index(['theme']);
            $table->index(['language']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_preferences');
    }
};
