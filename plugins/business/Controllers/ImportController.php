<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Business\Models\Business;
use Plugins\Business\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Plugins\Business\Imports\BusinessImport;
use Plugins\Business\Imports\ContactImport;
use Plugins\Business\Exports\BusinessSampleExport;
use Plugins\Business\Exports\ContactSampleExport;

class ImportController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            // Check if user has any business-related permissions
            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            // Check if user has import_businesses permission for imports
            if (!$user->hasPermission('import_businesses')) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Show the import interface
     */
    public function index(): View
    {
        return view('plugins.business::import.index');
    }

    /**
     * Import businesses from Excel file
     */
    public function importBusinesses(Request $request): RedirectResponse
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB max
        ]);

        try {
            $import = new BusinessImport();
            Excel::import($import, $request->file('file'));

            $summary = $import->getSummary();
            $errors = $import->getErrors();

            // Build success message with detailed information
            $messages = [];

            if ($summary['imported'] > 0) {
                $messages[] = "Successfully imported {$summary['imported']} new businesses.";
            }

            if ($summary['skipped'] > 0) {
                $messages[] = "{$summary['skipped']} businesses were skipped (already exist).";
            }

            if ($summary['errors'] > 0) {
                $messages[] = "{$summary['errors']} rows had errors.";
            }

            $message = implode(' ', $messages);

            if ($summary['imported'] > 0 || $summary['skipped'] > 0) {
                // Success if we imported or skipped records
                $message = $message ?: "Import completed. Processed {$summary['processed']} rows.";
                return redirect()->back()->with('success', $message)->with('import_errors', $errors);
            } else {
                // No records processed successfully
                $message = $message ?: 'No businesses were imported. Please check your file format.';
                return redirect()->back()->with('error', $message)->with('import_errors', $errors);
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Import failed: ' . $e->getMessage());
        }
    }

    /**
     * Import contacts from Excel file
     */
    public function importContacts(Request $request): RedirectResponse
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB max
        ]);

        try {
            $import = new ContactImport();
            Excel::import($import, $request->file('file'));

            $summary = $import->getSummary();
            $errors = $import->getErrors();

            // Build success message with detailed information
            $messages = [];

            if ($summary['imported'] > 0) {
                $messages[] = "Successfully imported {$summary['imported']} new contacts.";
            }

            if ($summary['skipped'] > 0) {
                $messages[] = "{$summary['skipped']} contacts were skipped (already exist).";
            }

            if ($summary['errors'] > 0) {
                $messages[] = "{$summary['errors']} rows had errors.";
            }

            $message = implode(' ', $messages);

            if ($summary['imported'] > 0 || $summary['skipped'] > 0) {
                // Success if we imported or skipped records
                $message = $message ?: "Import completed. Processed {$summary['processed']} rows.";
                return redirect()->back()->with('success', $message)->with('import_errors', $errors);
            } else {
                // No records processed successfully
                $message = $message ?: 'No contacts were imported. Please check your file format.';
                return redirect()->back()->with('error', $message)->with('import_errors', $errors);
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Import failed: ' . $e->getMessage());
        }
    }

    /**
     * Download business sample Excel file
     */
    public function downloadBusinessSample()
    {
        return Excel::download(new BusinessSampleExport(), 'business_import_sample.xlsx');
    }

    /**
     * Download contact sample Excel file
     */
    public function downloadContactSample()
    {
        return Excel::download(new ContactSampleExport(), 'contact_import_sample.xlsx');
    }
}
