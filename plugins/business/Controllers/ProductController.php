<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Business\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class ProductController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);
            
            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_businesses',
            'show' => 'view_businesses',
            'create' => 'manage_businesses',
            'store' => 'manage_businesses',
            'edit' => 'manage_businesses',
            'update' => 'manage_businesses',
            'destroy' => 'manage_businesses',
            'search' => 'view_businesses',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Display a listing of products
     */
    public function index(Request $request): View
    {
        $query = Product::withBusinessCount();

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }



        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $products = $query->latest()->paginate(20);

        return view('plugins.business::products.index', compact('products'));
    }

    /**
     * Show the form for creating a new product
     */
    public function create(): View
    {
        $availableIcons = Product::getAvailableIcons();

        return view('plugins.business::products.create', compact('availableIcons'));
    }

    /**
     * Store a newly created product
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'icon' => 'required|string|in:' . implode(',', array_keys(Product::getAvailableIcons())),
            'is_active' => 'boolean',
        ]);

        $product = Product::create([
            'name' => $request->name,
            'icon' => $request->icon,
            'is_active' => $request->boolean('is_active', true),
            'created_by' => Auth::id(),
        ]);

        // Ensure unique slug
        $product->ensureUniqueSlug();
        $product->save();

        return redirect()->route('products.index')
                        ->with('success', 'Product created successfully.');
    }

    /**
     * Display the specified product
     */
    public function show(Product $product): View
    {
        $product->load(['businesses', 'creator']);
        $stats = $product->getUsageStats();
        
        return view('plugins.business::products.show', compact('product', 'stats'));
    }

    /**
     * Show the form for editing the specified product
     */
    public function edit(Product $product): View
    {
        $availableIcons = Product::getAvailableIcons();

        return view('plugins.business::products.edit', compact('product', 'availableIcons'));
    }

    /**
     * Update the specified product
     */
    public function update(Request $request, Product $product): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'icon' => 'required|string|in:' . implode(',', array_keys(Product::getAvailableIcons())),
            'is_active' => 'boolean',
        ]);

        $product->update([
            'name' => $request->name,
            'icon' => $request->icon,
            'is_active' => $request->boolean('is_active', true),
        ]);

        // Ensure unique slug if name changed
        if ($product->wasChanged('name')) {
            $product->ensureUniqueSlug();
            $product->save();
        }

        return redirect()->route('products.index')
                        ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified product
     */
    public function destroy(Product $product): RedirectResponse
    {
        // Check if product is being used
        if ($product->businesses()->count() > 0) {
            return redirect()->route('products.index')
                            ->with('error', 'Cannot delete product that is assigned to businesses. Remove all assignments first.');
        }

        $product->delete();

        return redirect()->route('products.index')
                        ->with('success', 'Product deleted successfully.');
    }

    /**
     * Search products for autocomplete
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        
        $products = Product::active()
                           ->where('name', 'like', "%{$query}%")
                           ->limit(10)
                           ->get(['id', 'name', 'icon']);

        return response()->json($products);
    }
}
