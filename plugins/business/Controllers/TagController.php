<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Business\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class TagController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);
            
            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_businesses',
            'show' => 'view_businesses',
            'create' => 'manage_businesses',
            'store' => 'manage_businesses',
            'edit' => 'manage_businesses',
            'update' => 'manage_businesses',
            'destroy' => 'manage_businesses',
            'search' => 'view_businesses',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Display a listing of tags
     */
    public function index(Request $request): View
    {
        $query = Tag::withBusinessCount();

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $tags = $query->latest()->paginate(20);
        $predefinedColors = Tag::getPredefinedColors();

        return view('plugins.business::tags.index', compact('tags', 'predefinedColors'));
    }

    /**
     * Show the form for creating a new tag
     */
    public function create(): View
    {
        $predefinedColors = Tag::getPredefinedColors();
        return view('plugins.business::tags.create', compact('predefinedColors'));
    }

    /**
     * Store a newly created tag
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:tags,name',
            'color' => 'required|string|max:7',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $tag = Tag::create([
            'name' => $request->name,
            'color' => $request->color,
            'description' => $request->description,
            'is_active' => $request->boolean('is_active', true),
            'created_by' => Auth::id(),
        ]);

        // Ensure unique slug
        $tag->ensureUniqueSlug();
        $tag->save();

        // Handle AJAX requests
        if ($request->expectsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Tag created successfully.',
                'tag' => [
                    'id' => $tag->id,
                    'name' => $tag->name,
                    'color' => $tag->color,
                    'slug' => $tag->slug,
                ]
            ]);
        }

        return redirect()->route('tags.index')
                        ->with('success', 'Tag created successfully.');
    }

    /**
     * Display the specified tag
     */
    public function show(Tag $tag): View
    {
        $tag->load(['businesses', 'creator']);
        $stats = $tag->getUsageStats();
        
        return view('plugins.business::tags.show', compact('tag', 'stats'));
    }

    /**
     * Show the form for editing the specified tag
     */
    public function edit(Tag $tag): View
    {
        $predefinedColors = Tag::getPredefinedColors();
        return view('plugins.business::tags.edit', compact('tag', 'predefinedColors'));
    }

    /**
     * Update the specified tag
     */
    public function update(Request $request, Tag $tag): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:tags,name,' . $tag->id,
            'color' => 'required|string|max:7',
            'description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
        ]);

        $tag->update([
            'name' => $request->name,
            'color' => $request->color,
            'description' => $request->description,
            'is_active' => $request->boolean('is_active', true),
        ]);

        // Ensure unique slug if name changed
        if ($tag->wasChanged('name')) {
            $tag->ensureUniqueSlug();
            $tag->save();
        }

        return redirect()->route('tags.index')
                        ->with('success', 'Tag updated successfully.');
    }

    /**
     * Remove the specified tag
     */
    public function destroy(Tag $tag): RedirectResponse
    {
        // Check if tag is being used
        if ($tag->businesses()->count() > 0) {
            return redirect()->route('tags.index')
                            ->with('error', 'Cannot delete tag that is assigned to businesses. Remove all assignments first.');
        }

        $tag->delete();

        return redirect()->route('tags.index')
                        ->with('success', 'Tag deleted successfully.');
    }

    /**
     * Search tags for autocomplete
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        
        $tags = Tag::active()
                   ->where('name', 'like', "%{$query}%")
                   ->limit(10)
                   ->get(['id', 'name', 'color']);

        return response()->json($tags);
    }
}
