<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Business\Models\Business;
use Plugins\Business\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class ContactController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);
            
            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_businesses',
            'show' => 'view_businesses',
            'create' => 'manage_businesses',
            'store' => 'manage_businesses',
            'edit' => 'manage_businesses',
            'update' => 'manage_businesses',
            'destroy' => 'manage_businesses',
            'setPrimary' => 'manage_businesses',
            'bulkDestroy' => 'manage_businesses',
            'globalIndex' => 'view_businesses',
            'globalEdit' => 'manage_businesses',
            'globalUpdate' => 'manage_businesses',
            'globalDestroy' => 'manage_businesses',
            'globalBulkDestroy' => 'manage_businesses',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Display contacts for a business
     */
    public function index(Business $business): View
    {
        $contacts = $business->contacts()->latest()->get();
        return view('plugins.business::contacts.index', compact('business', 'contacts'));
    }

    /**
     * Show the form for creating a new contact
     */
    public function create(Business $business): View
    {
        return view('plugins.business::contacts.create', compact('business'));
    }

    /**
     * Store a newly created contact
     */
    public function store(Request $request, Business $business): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'arabic_name' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'phone_ext' => 'nullable|max:10',
            'phone2' => 'nullable|string|max:20',
            'phone2_ext' => 'nullable|max:10',
            'is_primary' => 'boolean',
            'notes' => 'nullable|string',
        ]);

        $contact = $business->contacts()->create([
            'name' => $request->name,
            'arabic_name' => $request->arabic_name,
            'position' => $request->position,
            'department' => $request->department,
            'email' => $request->email,
            'phone' => $request->phone,
            'phone_ext' => $request->phone_ext,
            'phone2' => $request->phone2,
            'phone2_ext' => $request->phone2_ext,
            'is_primary' => $request->boolean('is_primary', false),
            'notes' => $request->notes,
        ]);

        // If this is set as primary, ensure it's the only primary contact
        if ($contact->is_primary) {
            $contact->setAsPrimary();
        }

        return redirect()->route('business.contacts.index', $business)
                        ->with('success', 'Contact created successfully.');
    }

    /**
     * Display the specified contact
     */
    public function show(Business $business, Contact $contact): View
    {
        // Ensure contact belongs to business
        if ($contact->business_id !== $business->id) {
            abort(404);
        }

        return view('plugins.business::contacts.show', compact('business', 'contact'));
    }

    /**
     * Show the form for editing the specified contact
     */
    public function edit(Business $business, Contact $contact): View
    {
        // Ensure contact belongs to business
        if ($contact->business_id !== $business->id) {
            abort(404);
        }

        return view('plugins.business::contacts.edit', compact('business', 'contact'));
    }

    /**
     * Update the specified contact
     */
    public function update(Request $request, Business $business, Contact $contact): RedirectResponse
    {
        // Ensure contact belongs to business
        if ($contact->business_id !== $business->id) {
            abort(404);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'arabic_name' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'phone_ext' => 'nullable|max:10',
            'phone2' => 'nullable|string|max:20',
            'phone2_ext' => 'nullable|max:10',
            'is_primary' => 'boolean',
            'notes' => 'nullable|string',
        ]);

        $contact->update([
            'name' => $request->name,
            'arabic_name' => $request->arabic_name,
            'position' => $request->position,
            'department' => $request->department,
            'email' => $request->email,
            'phone' => $request->phone,
            'phone_ext' => $request->phone_ext,
            'phone2' => $request->phone2,
            'phone2_ext' => $request->phone2_ext,
            'is_primary' => $request->boolean('is_primary', false),
            'notes' => $request->notes,
        ]);

        // If this is set as primary, ensure it's the only primary contact
        if ($contact->is_primary) {
            $contact->setAsPrimary();
        }

        return redirect()->route('business.contacts.index', $business)
                        ->with('success', 'Contact updated successfully.');
    }

    /**
     * Remove the specified contact
     */
    public function destroy(Business $business, Contact $contact): RedirectResponse
    {
        // Ensure contact belongs to business
        if ($contact->business_id !== $business->id) {
            abort(404);
        }

        $contact->delete();

        return redirect()->route('business.contacts.index', $business)
                        ->with('success', 'Contact deleted successfully.');
    }

    /**
     * Set contact as primary
     */
    public function setPrimary(Business $business, Contact $contact): RedirectResponse
    {
        // Ensure contact belongs to business
        if ($contact->business_id !== $business->id) {
            abort(404);
        }

        $contact->setAsPrimary();

        return redirect()->back()
                        ->with('success', 'Contact set as primary successfully.');
    }

    /**
     * Bulk delete contacts
     */
    public function bulkDestroy(Request $request, Business $business): RedirectResponse
    {
        $request->validate([
            'contact_ids' => 'required|array|min:1',
            'contact_ids.*' => 'exists:business_contacts,id',
        ]);

        $contactIds = $request->contact_ids;

        // Ensure all contacts belong to this business
        $contacts = Contact::whereIn('id', $contactIds)
                          ->where('business_id', $business->id)
                          ->get();

        if ($contacts->count() !== count($contactIds)) {
            return redirect()->back()
                            ->with('error', 'Some contacts do not belong to this business.');
        }

        // Check if any of the contacts to be deleted is primary
        $primaryContact = $contacts->where('is_primary', true)->first();
        if ($primaryContact) {
            return redirect()->back()
                            ->with('error', 'Cannot delete primary contact. Please set another contact as primary first.');
        }

        // Delete the contacts
        $deletedCount = Contact::whereIn('id', $contactIds)
                              ->where('business_id', $business->id)
                              ->delete();

        return redirect()->route('business.contacts.index', $business)
                        ->with('success', "Successfully deleted {$deletedCount} contact(s).");
    }

    /**
     * Display a global listing of all contacts
     */
    public function globalIndex(Request $request): View
    {
        $query = Contact::with(['business'])
                        ->orderBy('created_at', 'desc');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('arabic_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('position', 'like', "%{$search}%")
                  ->orWhere('department', 'like', "%{$search}%");
            });
        }

        // Filter by business
        if ($request->filled('business_id')) {
            $query->where('business_id', $request->business_id);
        }

        // Filter by primary contacts
        if ($request->filled('is_primary')) {
            $query->where('is_primary', $request->is_primary === '1');
        }

        $contacts = $query->paginate(20)->withQueryString();

        // Get businesses for filter dropdown
        $businesses = Business::orderBy('name')->get(['id', 'name']);

        return view('plugins.business::contacts.global-index', compact('contacts', 'businesses'));
    }

    /**
     * Display a contact globally (without business context)
     */
    public function globalShow(Contact $contact): View
    {
        return view('plugins.business::contacts.global-show', compact('contact'));
    }

    /**
     * Show the form for editing a contact globally (without business context)
     */
    public function globalEdit(Contact $contact): View
    {
        return view('plugins.business::contacts.global-edit', compact('contact'));
    }

    /**
     * Update a contact globally (without business context)
     */
    public function globalUpdate(Request $request, Contact $contact): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'arabic_name' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'phone_ext' => 'nullable|max:10',
            'phone2' => 'nullable|string|max:20',
            'phone2_ext' => 'nullable|max:10',
            'is_primary' => 'boolean',
            'notes' => 'nullable|string',
        ]);

        $contact->update([
            'name' => $request->name,
            'arabic_name' => $request->arabic_name,
            'position' => $request->position,
            'department' => $request->department,
            'email' => $request->email,
            'phone' => $request->phone,
            'phone_ext' => $request->phone_ext,
            'phone2' => $request->phone2,
            'phone2_ext' => $request->phone2_ext,
            'is_primary' => $request->boolean('is_primary', false),
            'notes' => $request->notes,
        ]);

        // If this is set as primary and has a business, ensure it's the only primary contact for that business
        if ($contact->is_primary && $contact->business) {
            $contact->setAsPrimary();
        }

        return redirect()->route('contacts.index')
                        ->with('success', 'Contact updated successfully.');
    }

    /**
     * Delete a contact globally (without business context)
     */
    public function globalDestroy(Contact $contact): RedirectResponse
    {
        $contact->delete();

        return redirect()->route('contacts.index')
                        ->with('success', 'Contact deleted successfully.');
    }

    /**
     * Show the form for searching and adding existing contacts to a business
     */
    public function search(Business $business): View
    {
        return view('plugins.business::contacts.search', compact('business'));
    }

    /**
     * Search for contacts to add to a business
     */
    public function searchContacts(Request $request, Business $business): JsonResponse
    {
        $request->validate([
            'search' => 'required|string|min:2|max:255',
        ]);

        $search = $request->get('search');

        // Get contacts that don't already belong to this business
        $existingContactIds = $business->contacts()->pluck('id')->toArray();

        $contacts = Contact::where(function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('arabic_name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('phone2', 'like', "%{$search}%")
                      ->orWhere('position', 'like', "%{$search}%")
                      ->orWhere('department', 'like', "%{$search}%");
            })
            ->whereNotIn('id', $existingContactIds)
            ->with('business')
            ->limit(10)
            ->get();

        return response()->json([
            'contacts' => $contacts->map(function ($contact) {
                return [
                    'id' => $contact->id,
                    'name' => $contact->name,
                    'arabic_name' => $contact->arabic_name,
                    'email' => $contact->email,
                    'phone' => $contact->phone,
                    'phone2' => $contact->phone2,
                    'position' => $contact->position,
                    'department' => $contact->department,
                    'business_name' => $contact->business->name,
                    'business_id' => $contact->business_id,
                    'display_name' => $contact->name . ($contact->arabic_name ? " ({$contact->arabic_name})" : ''),
                    'contact_info' => $contact->email ?: ($contact->phone ?: $contact->phone2),
                ];
            })
        ]);
    }

    /**
     * Copy an existing contact to a business
     */
    public function copyContact(Request $request, Business $business): RedirectResponse
    {
        $request->validate([
            'contact_id' => 'required|exists:business_contacts,id',
            'copy_mode' => 'required|in:copy,move',
        ]);

        $originalContact = Contact::findOrFail($request->contact_id);

        // Check if contact already belongs to this business
        if ($originalContact->business_id === $business->id) {
            return redirect()->back()
                            ->with('error', 'This contact already belongs to this business.');
        }

        if ($request->copy_mode === 'move') {
            // Move the contact to the new business
            $originalContact->update([
                'business_id' => $business->id,
                'is_primary' => false, // Reset primary status when moving
            ]);

            $message = 'Contact moved successfully.';
        } else {
            // Copy the contact to the new business
            $newContact = $business->contacts()->create([
                'name' => $originalContact->name,
                'arabic_name' => $originalContact->arabic_name,
                'position' => $originalContact->position,
                'department' => $originalContact->department,
                'email' => $originalContact->email,
                'phone' => $originalContact->phone,
                'phone_ext' => $originalContact->phone_ext,
                'phone2' => $originalContact->phone2,
                'phone2_ext' => $originalContact->phone2_ext,
                'is_primary' => false, // Don't copy primary status
                'notes' => $originalContact->notes,
            ]);

            $message = 'Contact copied successfully.';
        }

        return redirect()->route('business.contacts.index', $business)
                        ->with('success', $message);
    }

    /**
     * Bulk delete contacts globally (without business context)
     */
    public function globalBulkDestroy(Request $request): RedirectResponse
    {
        $request->validate([
            'contact_ids' => 'required|array|min:1',
            'contact_ids.*' => 'exists:business_contacts,id',
        ]);

        $contactIds = $request->contact_ids;

        // Get the contacts to check for primary contacts
        $contacts = Contact::whereIn('id', $contactIds)->get();

        // Check if any of the contacts to be deleted is primary
        $primaryContacts = $contacts->where('is_primary', true);
        if ($primaryContacts->count() > 0) {
            $businessNames = $primaryContacts->pluck('business.name')->unique()->implode(', ');
            return redirect()->back()
                            ->with('error', "Cannot delete primary contacts from: {$businessNames}. Please set other contacts as primary first.");
        }

        // Delete the contacts
        $deletedCount = Contact::whereIn('id', $contactIds)->delete();

        return redirect()->route('contacts.index')
                        ->with('success', "Successfully deleted {$deletedCount} contact(s).");
    }
}
