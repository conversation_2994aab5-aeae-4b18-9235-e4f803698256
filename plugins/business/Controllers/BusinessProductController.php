<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Business\Models\Business;
use Plugins\Products\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class BusinessProductController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);
            
            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_businesses',
            'assign' => 'manage_businesses',
            'update' => 'manage_businesses',
            'remove' => 'manage_businesses',
            'search' => 'view_businesses',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Display products for a business
     */
    public function index(Business $business): View
    {
        $assignedProducts = $business->products()->with('creator')->get();
        $availableProducts = Product::active()
                                  ->whereNotIn('id', $assignedProducts->pluck('id'))
                                  ->orderBy('name')
                                  ->get();

        return view('plugins.business::business-products.index', compact('business', 'assignedProducts', 'availableProducts'));
    }

    /**
     * Assign products to business
     */
    public function assign(Request $request, Business $business): RedirectResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'custom_price' => 'nullable|numeric|min:0',
            'pricing_model' => 'nullable|in:monthly,yearly,one-time,custom',
            'status' => 'required|in:active,inactive,pending',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'notes' => 'nullable|string|max:1000',
            'custom_features' => 'nullable|array',
            'custom_features.*' => 'string|max:255',
        ]);

        $product = Product::findOrFail($request->product_id);

        // Check if product is already assigned
        if ($business->products()->where('product_id', $product->id)->exists()) {
            return redirect()->route('business.products.index', $business)
                            ->with('error', "Product '{$product->name}' is already assigned to this business.");
        }

        $options = [
            'status' => $request->status ?: 'active',
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'implementation_date' => $request->implementation_date,
            'contract_value' => $request->contract_value,
            'renewal_date' => $request->renewal_date,
            'product_version' => $request->product_version,
            'notes' => $request->notes,
            'custom_features' => $request->custom_features ? array_filter($request->custom_features) : null,
        ];

        $business->assignProduct($product, Auth::user(), $options);

        return redirect()->route('business.products.index', $business)
                        ->with('success', "Product '{$product->name}' assigned to {$business->name} successfully.");
    }

    /**
     * Update product assignment
     */
    public function update(Request $request, Business $business, Product $product): RedirectResponse
    {
        $request->validate([
            'custom_price' => 'nullable|numeric|min:0',
            'pricing_model' => 'nullable|in:monthly,yearly,one-time,custom',
            'status' => 'required|in:active,inactive,pending,cancelled',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'notes' => 'nullable|string|max:1000',
            'custom_features' => 'nullable|array',
            'custom_features.*' => 'string|max:255',
        ]);

        // Check if product is assigned to business
        if (!$business->products()->where('product_id', $product->id)->exists()) {
            abort(404, 'Product not assigned to this business.');
        }

        $business->products()->updateExistingPivot($product->id, [
            'custom_price' => $request->custom_price,
            'pricing_model' => $request->pricing_model ?: $product->pricing_model,
            'status' => $request->status,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'notes' => $request->notes,
            'custom_features' => $request->custom_features ? array_filter($request->custom_features) : null,
            'updated_at' => now(),
        ]);

        return redirect()->route('business.products.index', $business)
                        ->with('success', "Product assignment updated successfully.");
    }

    /**
     * Remove product from business
     */
    public function remove(Business $business, Product $product): RedirectResponse
    {
        $business->removeProduct($product);

        return redirect()->route('business.products.index', $business)
                        ->with('success', "Product '{$product->name}' removed from {$business->name}.");
    }

    /**
     * Search available products for assignment
     */
    public function search(Request $request, Business $business): JsonResponse
    {
        $query = $request->get('q', '');
        
        $assignedProductIds = $business->products()->pluck('products.id');
        
        $products = Product::active()
                          ->where('name', 'like', "%{$query}%")
                          ->whereNotIn('id', $assignedProductIds)
                          ->limit(10)
                          ->get(['id', 'name', 'category', 'base_price', 'pricing_model']);

        return response()->json($products);
    }

    /**
     * Show assignment form
     */
    public function create(Business $business): View
    {
        $availableProducts = Product::active()
                                  ->whereNotIn('id', $business->products()->pluck('products.id'))
                                  ->orderBy('name')
                                  ->get();
        
        $pricingModels = Product::getPricingModels();
        
        return view('plugins.business::business-products.create', compact('business', 'availableProducts', 'pricingModels'));
    }

    /**
     * Show edit assignment form
     */
    public function edit(Business $business, Product $product): View
    {
        // Check if product is assigned to business
        $assignment = $business->products()->where('product_id', $product->id)->first();
        
        if (!$assignment) {
            abort(404, 'Product not assigned to this business.');
        }

        $pricingModels = Product::getPricingModels();
        
        return view('plugins.business::business-products.edit', compact('business', 'product', 'assignment', 'pricingModels'));
    }
}
