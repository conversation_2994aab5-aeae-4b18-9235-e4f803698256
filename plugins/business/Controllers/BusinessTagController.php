<?php

namespace Plugins\Business\Controllers;

use App\Http\Controllers\Controller;
use Plugins\Business\Models\Business;
use Plugins\Business\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class BusinessTagController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = Auth::user();
            
            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);
            
            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        $permissions = [
            'index' => 'view_businesses',
            'assign' => 'manage_businesses',
            'remove' => 'manage_businesses',
            'search' => 'view_businesses',
        ];

        return $permissions[$action] ?? null;
    }

    /**
     * Display tags for a business
     */
    public function index(Business $business): View
    {
        $assignedTags = $business->tags()->with('creator')->get();
        $availableTags = Tag::active()
                           ->whereNotIn('id', $assignedTags->pluck('id'))
                           ->orderBy('name')
                           ->get();

        return view('plugins.business::business-tags.index', compact('business', 'assignedTags', 'availableTags'));
    }

    /**
     * Assign tags to business
     */
    public function assign(Request $request, Business $business): RedirectResponse
    {
        $request->validate([
            'tag_ids' => 'required|array',
            'tag_ids.*' => 'exists:tags,id',
        ]);

        $user = Auth::user();
        $assignedCount = 0;

        foreach ($request->tag_ids as $tagId) {
            // Check if tag is already assigned
            if (!$business->tags()->where('tag_id', $tagId)->exists()) {
                $business->assignTag(Tag::find($tagId), $user);
                $assignedCount++;
            }
        }

        $message = $assignedCount > 0 
            ? "Successfully assigned {$assignedCount} tag(s) to {$business->name}."
            : "No new tags were assigned (tags may already be assigned).";

        return redirect()->route('business.tags.index', $business)
                        ->with('success', $message);
    }

    /**
     * Remove tag from business
     */
    public function remove(Business $business, Tag $tag): RedirectResponse
    {
        $business->removeTag($tag);

        return redirect()->route('business.tags.index', $business)
                        ->with('success', "Tag '{$tag->name}' removed from {$business->name}.");
    }

    /**
     * Search available tags for assignment
     */
    public function search(Request $request, Business $business): JsonResponse
    {
        $query = $request->get('q', '');
        
        $assignedTagIds = $business->tags()->pluck('tags.id');
        
        $tags = Tag::active()
                   ->where('name', 'like', "%{$query}%")
                   ->whereNotIn('id', $assignedTagIds)
                   ->limit(10)
                   ->get(['id', 'name', 'color']);

        return response()->json($tags);
    }

    /**
     * Bulk assign tags via AJAX
     */
    public function bulkAssign(Request $request, Business $business): JsonResponse
    {
        $request->validate([
            'tag_ids' => 'required|array',
            'tag_ids.*' => 'exists:tags,id',
        ]);

        $user = Auth::user();
        $assignedTags = [];

        foreach ($request->tag_ids as $tagId) {
            $tag = Tag::find($tagId);
            if ($tag && !$business->tags()->where('tag_id', $tagId)->exists()) {
                $business->assignTag($tag, $user);
                $assignedTags[] = [
                    'id' => $tag->id,
                    'name' => $tag->name,
                    'color' => $tag->color,
                ];
            }
        }

        return response()->json([
            'success' => true,
            'message' => count($assignedTags) . ' tag(s) assigned successfully.',
            'assigned_tags' => $assignedTags,
        ]);
    }

    /**
     * Remove tag via AJAX
     */
    public function ajaxRemove(Business $business, Tag $tag): JsonResponse
    {
        $business->removeTag($tag);

        return response()->json([
            'success' => true,
            'message' => "Tag '{$tag->name}' removed successfully.",
        ]);
    }
}
