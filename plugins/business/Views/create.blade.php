@extends('layouts.app')

@section('title', 'Create Business')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Create New Business</h1>
            <a href="{{ route('business.index') }}" 
               class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Back to Businesses
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <form method="POST" action="{{ route('business.store') }}" class="p-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h3>
                    </div>

                    <!-- Business Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Business Name <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('name') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">The main business name used for identification</p>
                    </div>

                    <!-- Brand Name -->
                    <div>
                        <label for="brand_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Brand Name</label>
                        <input type="text" name="brand_name" id="brand_name" value="{{ old('brand_name') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('brand_name') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('brand_name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Marketing or public-facing brand name (if different from business name)</p>
                    </div>

                    <!-- Legal Name -->
                    <div>
                        <label for="legal_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Legal Name</label>
                        <input type="text" name="legal_name" id="legal_name" value="{{ old('legal_name') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('legal_name') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('legal_name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Official legal entity name (if different from business name)</p>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                        <input type="email" name="email" id="email" value="{{ old('email') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('email') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('email')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                        <textarea name="description" id="description" rows="3"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('description') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Taqnyat Integration -->
                    <div class="md:col-span-2 mt-6">
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <div class="mb-4">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Taqnyat Integration</h3>
                                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                    Configure Taqnyat SMS service integration for this business
                                </p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Taqnyat ID -->
                                <div>
                                    <label for="taqnyat_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Taqnyat ID</label>
                                    <input type="text" name="taqnyat_id" id="taqnyat_id"
                                           value="{{ old('taqnyat_id') }}"
                                           class="mt-1 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white"
                                           placeholder="Enter Taqnyat account ID">
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Your Taqnyat account identifier</p>
                                    @error('taqnyat_id')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Taqnyat Username -->
                                <div>
                                    <label for="taqnyat_username" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Taqnyat Username</label>
                                    <input type="text" name="taqnyat_username" id="taqnyat_username"
                                           value="{{ old('taqnyat_username') }}"
                                           class="mt-1 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white"
                                           placeholder="Enter Taqnyat username">
                                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Your Taqnyat account username</p>
                                    @error('taqnyat_username')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Contact Information</h3>
                    </div>

                    <!-- Primary Phone -->
                    <div>
                        <label for="primary_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Primary Phone</label>
                        <input type="tel" name="primary_phone" id="primary_phone" value="{{ old('primary_phone') }}"
                               placeholder="(*************"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('primary_phone') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('primary_phone')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Main business phone number</p>
                    </div>

                    <!-- Secondary Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Secondary Phone</label>
                        <input type="tel" name="phone" id="phone" value="{{ old('phone') }}"
                               placeholder="(*************"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('phone') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('phone')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Additional phone number (optional)</p>
                    </div>

                    <!-- Primary Website -->
                    <div>
                        <label for="website_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Primary Website</label>
                        <input type="url" name="website_url" id="website_url" value="{{ old('website_url') }}"
                               placeholder="https://www.example.com"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('website_url') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('website_url')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Main business website URL</p>
                    </div>

                    <!-- Secondary Website -->
                    <div>
                        <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Secondary Website</label>
                        <input type="url" name="website" id="website" value="{{ old('website') }}"
                               placeholder="https://www.example.com"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('website') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('website')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Additional website URL (optional)</p>
                    </div>

                    <!-- Address Information -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Address Information</h3>
                    </div>

                    <!-- Address -->
                    <div class="md:col-span-2">
                        <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Address</label>
                        <textarea name="address" id="address" rows="2"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('address') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">{{ old('address') }}</textarea>
                        @error('address')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- City -->
                    <div>
                        <label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300">City</label>
                        <input type="text" name="city" id="city" value="{{ old('city') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('city') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('city')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- State -->
                    <div>
                        <label for="state" class="block text-sm font-medium text-gray-700 dark:text-gray-300">State/Province</label>
                        <input type="text" name="state" id="state" value="{{ old('state') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('state') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('state')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Country -->
                    <div>
                        <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Country</label>
                        <input type="text" name="country" id="country" value="{{ old('country') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('country') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('country')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Postal Code -->
                    <div>
                        <label for="postal_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Postal Code</label>
                        <input type="text" name="postal_code" id="postal_code" value="{{ old('postal_code') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('postal_code') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('postal_code')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Additional Information -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Additional Information</h3>
                    </div>

                    <!-- Tax ID -->
                    <div>
                        <label for="tax_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tax ID</label>
                        <input type="text" name="tax_id" id="tax_id" value="{{ old('tax_id') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('tax_id') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('tax_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Business Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Business Status</label>
                        <select name="status" id="status"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('status') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                            @foreach($statuses as $key => $label)
                                <option value="{{ $key }}" {{ old('status', 'lead') === $key ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Churn Reason (conditional) -->
                    <div id="churn-reason-section" class="{{ old('status') === 'churned' ? '' : 'hidden' }}">
                        <label for="churn_reason" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Churn Reason</label>
                        <textarea name="churn_reason" id="churn_reason" rows="3"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('churn_reason') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">{{ old('churn_reason') }}</textarea>
                        @error('churn_reason')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            Please explain why this business churned
                        </p>
                    </div>

                    <!-- Churn Date (conditional) -->
                    <div id="churn-date-section" class="{{ old('status') === 'churned' ? '' : 'hidden' }}">
                        <label for="churned_at" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Churn Date</label>
                        <input type="datetime-local" name="churned_at" id="churned_at"
                               value="{{ old('churned_at') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('churned_at') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('churned_at')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            When did this business churn?
                        </p>
                    </div>

                    <!-- Lost Reason (conditional) -->
                    <div id="lost-reason-section" class="{{ old('status') === 'lost' ? '' : 'hidden' }}">
                        <label for="lost_reason" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Lost Reason</label>
                        <textarea name="lost_reason" id="lost_reason" rows="3"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('lost_reason') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">{{ old('lost_reason') }}</textarea>
                        @error('lost_reason')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            Please explain why this business was lost
                        </p>
                    </div>

                    <!-- Lost Date (conditional) -->
                    <div id="lost-date-section" class="{{ old('status') === 'lost' ? '' : 'hidden' }}">
                        <label for="lost_at" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Lost Date</label>
                        <input type="datetime-local" name="lost_at" id="lost_at"
                               value="{{ old('lost_at') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('lost_at') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        @error('lost_at')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            When was this business lost?
                        </p>
                    </div>

                    <!-- Tags Section -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Tags & Products</h3>
                    </div>

                    <!-- Tags -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md p-4">
                            @foreach($tags as $tag)
                                <label class="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 rounded cursor-pointer transition duration-150 ease-in-out">
                                    <input type="checkbox" name="tag_ids[]" value="{{ $tag->id }}"
                                           {{ in_array($tag->id, old('tag_ids', [])) ? 'checked' : '' }}
                                           class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white">
                                    <div class="ml-3 flex items-center">
                                        <div class="w-3 h-3 rounded-full mr-2" style="background-color: {{ $tag->color }};"></div>
                                        <span class="text-sm text-gray-900 dark:text-white">{{ $tag->name }}</span>
                                    </div>
                                </label>
                            @endforeach
                        </div>
                        @error('tag_ids')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <div class="mt-2 flex justify-between items-center">
                            <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                Select tags to categorize this business
                            </p>
                            @if(auth()->user()->hasPermission('manage_businesses'))
                                <button type="button" onclick="openCreateTagModal()"
                                        class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-sm font-medium">
                                    <i class="fas fa-plus mr-1"></i>Create New Tag
                                </button>
                            @endif
                        </div>
                    </div>

                    <!-- Products -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Products</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md p-4">
                            @foreach($products as $product)
                                <label class="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 rounded cursor-pointer transition duration-150 ease-in-out">
                                    <input type="checkbox" name="product_ids[]" value="{{ $product->id }}"
                                           {{ in_array($product->id, old('product_ids', [])) ? 'checked' : '' }}
                                           class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white">
                                    <div class="ml-3 flex items-center">
                                        <i class="{{ $product->icon }} text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-2"></i>
                                        <span class="text-sm text-gray-900 dark:text-white">{{ $product->name }}</span>
                                    </div>
                                </label>
                            @endforeach
                        </div>
                        @error('product_ids')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            Select products/services for this business
                        </p>
                    </div>
                </div>

                <!-- WhatsApp Business Integration -->
                <div class="mt-8">
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-8">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white">WhatsApp Business Integration</h3>
                                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                    Configure WhatsApp Business API integration for this business
                                </p>
                            </div>
                            <div class="flex items-center">
                                <input type="hidden" name="whatsapp_enabled" value="0">
                                <input type="checkbox" id="whatsapp_enabled" name="whatsapp_enabled" value="1"
                                       {{ old('whatsapp_enabled') ? 'checked' : '' }}
                                       class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                                <label for="whatsapp_enabled" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Enable WhatsApp</label>
                            </div>
                        </div>

                        <div id="whatsapp-fields" class="grid grid-cols-1 md:grid-cols-2 gap-6 {{ old('whatsapp_enabled') ? '' : 'hidden' }}">
                            <!-- Meta Business ID -->
                            <div>
                                <label for="meta_business_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Meta Business ID</label>
                                <input type="text" name="meta_business_id" id="meta_business_id"
                                       value="{{ old('meta_business_id') }}"
                                       class="mt-1 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white"
                                       placeholder="Enter Meta Business Account ID">
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Your Facebook/Meta Business Account identifier</p>
                                @error('meta_business_id')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- WhatsApp ID -->
                            <div>
                                <label for="whatsapp_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">WhatsApp Business ID</label>
                                <input type="text" name="whatsapp_id" id="whatsapp_id"
                                       value="{{ old('whatsapp_id') }}"
                                       class="mt-1 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white"
                                       placeholder="Enter WhatsApp Business Account ID">
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Your WhatsApp Business Account identifier</p>
                                @error('whatsapp_id')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- WhatsApp Provider -->
                            <div>
                                <label for="whatsapp_provider" class="block text-sm font-medium text-gray-700 dark:text-gray-300">WhatsApp Provider</label>
                                <select name="whatsapp_provider" id="whatsapp_provider"
                                        class="mt-1 block w-full py-2 px-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 sm:text-sm transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                                    <option value="">Select Provider</option>
                                    @foreach(\Plugins\Business\Models\Business::getWhatsAppProviders() as $value => $label)
                                        <option value="{{ $value }}" {{ old('whatsapp_provider') == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Choose your WhatsApp Business API provider</p>
                                @error('whatsapp_provider')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Message Quality -->
                            <div>
                                <label for="message_quality" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Message Quality Rating</label>
                                <input type="text" name="message_quality" id="message_quality"
                                       value="{{ old('message_quality') }}"
                                       class="mt-1 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white"
                                       placeholder="e.g., High, Medium, Low">
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Current message quality rating from WhatsApp</p>
                                @error('message_quality')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Messaging Tier -->
                            <div>
                                <label for="messaging_tier" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Messaging Tier</label>
                                <select name="messaging_tier" id="messaging_tier"
                                        class="mt-1 block w-full py-2 px-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 sm:text-sm transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                                    <option value="">Select Tier</option>
                                    @foreach(\Plugins\Business\Models\Business::getMessagingTiers() as $value => $label)
                                        <option value="{{ $value }}" {{ old('messaging_tier') == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Current WhatsApp Business messaging tier</p>
                                @error('messaging_tier')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Meta Business Verified -->
                            <div>
                                <div class="flex items-center">
                                    <input type="hidden" name="meta_business_verified" value="0">
                                    <input type="checkbox" id="meta_business_verified" name="meta_business_verified" value="1"
                                           {{ old('meta_business_verified') ? 'checked' : '' }}
                                           class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                                    <label for="meta_business_verified" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Meta Business Verified</label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Mark if Meta Business account is verified</p>
                                @error('meta_business_verified')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- WhatsApp Business Verified -->
                            <div>
                                <div class="flex items-center">
                                    <input type="hidden" name="whatsapp_business_verified" value="0">
                                    <input type="checkbox" id="whatsapp_business_verified" name="whatsapp_business_verified" value="1"
                                           {{ old('whatsapp_business_verified') ? 'checked' : '' }}
                                           class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                                    <label for="whatsapp_business_verified" class="ml-2 text-sm text-gray-700 dark:text-gray-300">WhatsApp Business Verified</label>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Mark if WhatsApp Business account is verified</p>
                                @error('whatsapp_business_verified')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Submit Buttons -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('business.index') }}" 
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Create Business
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Tag Modal -->
<div id="createTagModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 transition duration-150 ease-in-out">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800 transition duration-150 ease-in-out">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Create New Tag</h3>
                <button type="button" onclick="closeCreateTagModal()" class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createTagForm">
                @csrf
                <div class="mb-4">
                    <label for="tag_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tag Name</label>
                    <input type="text" id="tag_name" name="name" required
                           class="w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white"
                           placeholder="Enter tag name">
                </div>
                <div class="mb-4">
                    <label for="tag_color" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tag Color</label>
                    <div class="flex space-x-2">
                        <input type="color" id="tag_color" name="color" value="#3B82F6"
                               class="w-12 h-10 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                        <input type="text" id="tag_color_text" value="#3B82F6"
                               class="flex-1 rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white"
                               placeholder="#3B82F6">
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeCreateTagModal()"
                            class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 transition duration-150 ease-in-out">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 transition duration-150 ease-in-out">
                        Create Tag
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Tag creation modal functions
function openCreateTagModal() {
    document.getElementById('createTagModal').classList.remove('hidden');
}

function closeCreateTagModal() {
    document.getElementById('createTagModal').classList.add('hidden');
    document.getElementById('createTagForm').reset();
}

// Handle tag creation
document.getElementById('createTagForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch('{{ route("tags.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add new tag to the tags list
            const tagsContainer = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3');
            const newTagHtml = `
                <label class="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 rounded cursor-pointer transition duration-150 ease-in-out">
                    <input type="checkbox" name="tag_ids[]" value="${data.tag.id}" checked
                           class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                    <div class="ml-3 flex items-center">
                        <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${data.tag.color};"></div>
                        <span class="text-sm text-gray-900 dark:text-white">${data.tag.name}</span>
                    </div>
                </label>
            `;
            tagsContainer.insertAdjacentHTML('beforeend', newTagHtml);

            closeCreateTagModal();

            // Show success message
            const successMessage = document.createElement('div');
            successMessage.className = 'mb-4 bg-green-100 dark:bg-green-900 border border-green-400 text-green-700 px-4 py-3 rounded relative';
            successMessage.innerHTML = `<span class="block sm:inline">Tag "${data.tag.name}" created and selected successfully!</span>`;
            document.querySelector('.max-w-7xl').insertBefore(successMessage, document.querySelector('.bg-white dark:bg-gray-800.shadow'));

            setTimeout(() => {
                successMessage.remove();
            }, 5000);
        } else {
            alert('Error creating tag: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating tag. Please try again.');
    });
});

// Sync color picker and text input
document.getElementById('tag_color').addEventListener('change', function() {
    document.getElementById('tag_color_text').value = this.value;
});

document.getElementById('tag_color_text').addEventListener('change', function() {
    document.getElementById('tag_color').value = this.value;
});

document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status');
    const churnReasonSection = document.getElementById('churn-reason-section');
    const churnDateSection = document.getElementById('churn-date-section');
    const lostReasonSection = document.getElementById('lost-reason-section');
    const lostDateSection = document.getElementById('lost-date-section');

    function toggleConditionalFields() {
        // Handle churn fields
        if (statusSelect.value === 'churned') {
            churnReasonSection.classList.remove('hidden');
            churnDateSection.classList.remove('hidden');
        } else {
            churnReasonSection.classList.add('hidden');
            churnDateSection.classList.add('hidden');
        }

        // Handle lost fields
        if (statusSelect.value === 'lost') {
            lostReasonSection.classList.remove('hidden');
            lostDateSection.classList.remove('hidden');
        } else {
            lostReasonSection.classList.add('hidden');
            lostDateSection.classList.add('hidden');
        }
    }

    statusSelect.addEventListener('change', toggleConditionalFields);
    toggleConditionalFields(); // Initial check

    // WhatsApp fields toggle
    const whatsappEnabledCheckbox = document.getElementById('whatsapp_enabled');
    const whatsappFields = document.getElementById('whatsapp-fields');

    function toggleWhatsAppFields() {
        if (whatsappEnabledCheckbox.checked) {
            whatsappFields.classList.remove('hidden');
        } else {
            whatsappFields.classList.add('hidden');
        }
    }

    whatsappEnabledCheckbox.addEventListener('change', toggleWhatsAppFields);
    toggleWhatsAppFields(); // Initial check
});
</script>
@endsection
