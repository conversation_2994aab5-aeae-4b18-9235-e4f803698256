@extends('layouts.app')

@section('title', 'Activity Details')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Activity Details</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">{{ $business->name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('business.show', $business) }}#activity" 
                   class="bg-gray-500 dark:bg-gray-700 hover:bg-gray-600 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Business
                </a>
                @if($activity->user_id === auth()->id() || auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('business.activities.edit', [$business, $activity]) }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Edit Activity
                    </a>
                @endif
            </div>
        </div>

        <!-- Activity Details -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        @php
                            $activityTypes = \Plugins\Business\Models\BusinessActivity::getActivityTypes();
                            $typeInfo = $activityTypes[$activity->type] ?? ['icon' => 'fas fa-comment', 'color' => 'blue'];
                        @endphp
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-{{ $typeInfo['color'] }}-100 dark:bg-{{ $typeInfo['color'] }}-900 rounded-full flex items-center justify-center">
                                <i class="{{ $typeInfo['icon'] }} text-{{ $typeInfo['color'] }}-600 dark:text-{{ $typeInfo['color'] }}-400"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $activity->title }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ ucfirst($activity->type) }} • 
                                {{ $activity->created_at->format('M j, Y g:i A') }} • 
                                {{ $activity->user->name ?? 'Unknown' }}
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $typeInfo['color'] }}-100 dark:bg-{{ $typeInfo['color'] }}-900 text-{{ $typeInfo['color'] }}-800 dark:text-{{ $typeInfo['color'] }}-200">
                            {{ ucfirst($activity->category) }}
                        </span>
                        @if(!$activity->is_system_generated)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                Manual
                            </span>
                        @endif
                    </div>
                </div>
            </div>

            <div class="px-6 py-4">
                <div class="prose dark:prose-invert max-w-none">
                    <p class="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{ $activity->description }}</p>
                </div>

                <!-- Metadata -->
                @if($activity->metadata && count($activity->metadata) > 0)
                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Additional Information</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($activity->metadata as $key => $value)
                                @if($key !== 'edit_history' && !is_array($value))
                                    <div>
                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ ucfirst(str_replace('_', ' ', $key)) }}</dt>
                                        <dd class="text-sm text-gray-900 dark:text-white">{{ $value }}</dd>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Edit History -->
                @if(isset($activity->metadata['edit_history']) && count($activity->metadata['edit_history']) > 0)
                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Edit History</h4>
                        <div class="space-y-3">
                            @foreach($activity->metadata['edit_history'] as $edit)
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-md p-3">
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">
                                        Edited {{ \Carbon\Carbon::parse($edit['edited_at'])->format('M j, Y g:i A') }}
                                    </div>
                                    <div class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                                        {{ $edit['content'] }}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Attachments -->
                @if($activity->attachments && $activity->attachments->count() > 0)
                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Attachments</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($activity->attachments as $attachment)
                                <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                                    <i class="fas fa-file text-gray-400 dark:text-gray-500 mr-3"></i>
                                    <div class="flex-1 min-w-0">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ $attachment->original_name }}</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ number_format($attachment->file_size / 1024, 1) }} KB • 
                                            {{ $attachment->created_at->format('M j, Y') }}
                                        </div>
                                    </div>
                                    <a href="{{ route('business.activities.downloadAttachment', [$business, $activity, $attachment]) }}" 
                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Comments -->
                @if($activity->comments && $activity->comments->count() > 0)
                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Comments</h4>
                        <div class="space-y-4">
                            @foreach($activity->comments as $comment)
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-md p-4">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                                                <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                                                    {{ substr($comment->user->name ?? 'U', 0, 1) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                {{ $comment->user->name ?? 'Unknown' }}
                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">
                                                {{ $comment->created_at->format('M j, Y g:i A') }}
                                                @if($comment->updated_at != $comment->created_at)
                                                    • Edited
                                                @endif
                                            </div>
                                            <div class="text-sm text-gray-700 dark:text-gray-300">
                                                {!! $comment->content_html !!}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Actions -->
        @if($activity->user_id === auth()->id() || auth()->user()->hasPermission('manage_businesses'))
            <div class="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Actions</h4>
                <div class="flex space-x-3">
                    <a href="{{ route('business.activities.edit', [$business, $activity]) }}" 
                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Activity
                    </a>
                    <form method="POST" action="{{ route('business.activities.destroy', [$business, $activity]) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this activity? This action cannot be undone.')" 
                          class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                            <i class="fas fa-trash mr-2"></i>
                            Delete Activity
                        </button>
                    </form>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
