@extends('layouts.app')

@section('title', 'Edit Activity')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Activity</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Edit activity for {{ $business->name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('business.activities.show', [$business, $activity]) }}" 
                   class="bg-gray-500 dark:bg-gray-700 hover:bg-gray-600 dark:hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Activity
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <form method="POST" action="{{ route('business.activities.update', [$business, $activity]) }}" class="p-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 gap-6">
                    <!-- Activity Type -->
                    <div>
                        <label for="message_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Activity Type <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <select name="message_type" id="message_type" required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('message_type') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                            <option value="">Select activity type</option>
                            @foreach($messageTypes as $key => $label)
                                <option value="{{ $key }}" {{ (old('message_type', $activity->metadata['communication_type'] ?? $activity->type) === $key) ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @error('message_type')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Message -->
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Message <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <textarea name="message" id="message" rows="4" required
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('message') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white"
                                  placeholder="Enter your message or activity details...">{{ old('message', $activity->description) }}</textarea>
                        @error('message')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Maximum 1000 characters.
                        </p>
                    </div>

                    <!-- Edit History -->
                    @if(isset($activity->metadata['edit_history']) && count($activity->metadata['edit_history']) > 0)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Edit History
                        </label>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-md p-4 max-h-40 overflow-y-auto">
                            @foreach($activity->metadata['edit_history'] as $edit)
                                <div class="mb-3 last:mb-0 pb-3 last:pb-0 border-b last:border-b-0 border-gray-200 dark:border-gray-600">
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">
                                        Edited {{ \Carbon\Carbon::parse($edit['edited_at'])->format('M j, Y g:i A') }}
                                    </div>
                                    <div class="text-sm text-gray-700 dark:text-gray-300">
                                        {{ $edit['content'] }}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Current Attachments -->
                    @if($activity->attachments && $activity->attachments->count() > 0)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Current Attachments
                        </label>
                        <div class="space-y-2">
                            @foreach($activity->attachments as $attachment)
                                <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                                    <i class="fas fa-file text-gray-400 dark:text-gray-500 mr-3"></i>
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $attachment->original_name }}</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">{{ number_format($attachment->file_size / 1024, 1) }} KB</div>
                                    </div>
                                    <a href="{{ route('business.activities.downloadAttachment', [$business, $activity, $attachment]) }}" 
                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 mr-3">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Submit Buttons -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('business.activities.show', [$business, $activity]) }}" 
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Update Activity
                    </button>
                </div>
            </form>
        </div>

        <!-- Activity Info -->
        <div class="mt-6 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4 transition duration-150 ease-in-out">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Activity Information</h3>
                    <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Created: {{ $activity->created_at->format('M j, Y g:i A') }}</li>
                            <li>Created by: {{ $activity->user->name ?? 'Unknown' }}</li>
                            @if($activity->updated_at != $activity->created_at)
                                <li>Last updated: {{ $activity->updated_at->format('M j, Y g:i A') }}</li>
                            @endif
                            <li>Editing will create a history record of the original content</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
