@extends('layouts.app')

@section('title', 'Assign Product')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Assign Product</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Assign a product to {{ $business->name }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('business.products.index', $business) }}" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Products
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <form method="POST" action="{{ route('business.products.assign', $business) }}" class="p-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Product Selection -->
                    <div class="md:col-span-2">
                        <label for="product_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Product <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <select name="product_id" id="product_id" required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('product_id') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                            <option value="">Select a product</option>
                            @foreach($availableProducts as $product)
                                <option value="{{ $product->id }}" 
                                        data-base-price="{{ $product->base_price }}"
                                        data-pricing-model="{{ $product->pricing_model }}"
                                        data-features="{{ $product->features ? json_encode($product->features) : '[]' }}"
                                        {{ old('product_id', request('product')) == $product->id ? 'selected' : '' }}>
                                    {{ $product->name }} - {{ $product->category_label }}
                                    @if($product->base_price)
                                        ({{ $product->formatted_base_price }})
                                    @endif
                                </option>
                            @endforeach
                        </select>
                        @error('product_id')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Custom Price -->
                    <div>
                        <label for="custom_price" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Custom Price</label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" name="custom_price" id="custom_price" value="{{ old('custom_price') }}" 
                                   step="0.01" min="0" placeholder="0.00"
                                   class="pl-7 mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('custom_price') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        </div>
                        @error('custom_price')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Override the base price (leave empty to use base price)</p>
                    </div>

                    <!-- Pricing Model -->
                    <div>
                        <label for="pricing_model" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Pricing Model</label>
                        <select name="pricing_model" id="pricing_model"
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('pricing_model') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                            <option value="">Use product default</option>
                            @foreach($pricingModels as $key => $label)
                                <option value="{{ $key }}" {{ old('pricing_model') === $key ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @error('pricing_model')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Status <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <select name="status" id="status" required
                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('status') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                            <option value="active" {{ old('status', 'active') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="pending" {{ old('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Start Date -->
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Start Date</label>
                        <input type="date" name="start_date" id="start_date" value="{{ old('start_date') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('start_date') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        @error('start_date')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- End Date -->
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300">End Date</label>
                        <input type="date" name="end_date" id="end_date" value="{{ old('end_date') }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('end_date') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        @error('end_date')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Notes -->
                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                        <textarea name="notes" id="notes" rows="3"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('notes') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">{{ old('notes') }}</textarea>
                        @error('notes')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            Additional notes about this product assignment
                        </p>
                    </div>


                </div>

                <!-- Product Preview -->
                <div id="product-preview" class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hidden transition duration-150 ease-in-out">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Product Details</h4>
                    <div id="preview-content"></div>
                </div>

                <!-- Submit Buttons -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('business.products.index', $business) }}" 
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Assign Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const productSelect = document.getElementById('product_id');
    const customPriceInput = document.getElementById('custom_price');
    const pricingModelSelect = document.getElementById('pricing_model');
    const productPreview = document.getElementById('product-preview');
    const previewContent = document.getElementById('preview-content');

    productSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        
        if (selectedOption.value) {
            const basePrice = selectedOption.dataset.basePrice;
            const pricingModel = selectedOption.dataset.pricingModel;
            const features = JSON.parse(selectedOption.dataset.features || '[]');
            
            // Update custom price placeholder
            if (basePrice) {
                customPriceInput.placeholder = basePrice;
            }
            
            // Update pricing model
            if (pricingModel && !pricingModelSelect.value) {
                pricingModelSelect.value = pricingModel;
            }
            
            // Show product preview
            let html = '';
            if (basePrice) {
                html += `<p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Base Price: $${parseFloat(basePrice).toFixed(2)}</p>`;
            }
            if (pricingModel) {
                html += `<p class="text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Default Pricing Model: ${pricingModel}</p>`;
            }

            
            previewContent.innerHTML = html;
            productPreview.classList.remove('hidden');
        } else {
            productPreview.classList.add('hidden');
            customPriceInput.placeholder = '0.00';
        }
    });

    // Trigger change event if product is pre-selected
    if (productSelect.value) {
        productSelect.dispatchEvent(new Event('change'));
    }
});


</script>
@endsection
