@extends('layouts.app')

@section('title', 'Business Products')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Products: {{ $business->name }}</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Manage products and services for this business</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('business.show', $business) }}" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Business
                </a>
                @if(auth()->user()->hasPermission('manage_businesses') && $availableProducts->count() > 0)
                    <a href="{{ route('business.products.create', $business) }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Assign Product
                    </a>
                @endif
            </div>
        </div>

        <!-- Assigned Products -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg mb-8 transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                    Assigned Products ({{ $assignedProducts->count() }})
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Products currently assigned to this business</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                @if($assignedProducts->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Pricing</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Period</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                                @foreach($assignedProducts as $product)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $product->name }}</div>
                                                <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $product->category_label }}</div>
                                                @if($product->pivot->notes)
                                                    <div class="text-xs text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">{{ Str::limit($product->pivot->notes, 50) }}</div>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-white">
                                                @if($product->pivot->custom_price)
                                                    ${{ number_format($product->pivot->custom_price, 2) }}
                                                @elseif($product->base_price)
                                                    ${{ number_format($product->base_price, 2) }}
                                                @else
                                                    Custom pricing
                                                @endif
                                            </div>
                                            @if($product->pivot->pricing_model)
                                                <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ ucfirst($product->pivot->pricing_model) }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                {{ $product->pivot->status === 'active' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 
                                                   ($product->pivot->status === 'pending' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' : 
                                                   ($product->pivot->status === 'cancelled' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200')) }} transition duration-150 ease-in-out">
                                                {{ ucfirst($product->pivot->status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                            @if($product->pivot->start_date)
                                                <div>Start: {{ \Carbon\Carbon::parse($product->pivot->start_date)->format('M d, Y') }}</div>
                                            @endif
                                            @if($product->pivot->end_date)
                                                <div>End: {{ \Carbon\Carbon::parse($product->pivot->end_date)->format('M d, Y') }}</div>
                                            @endif
                                            @if(!$product->pivot->start_date && !$product->pivot->end_date)
                                                <span class="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">No dates set</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                @if(auth()->user()->hasPermission('manage_businesses'))
                                                    <a href="{{ route('business.products.edit', [$business, $product]) }}" 
                                                       class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300">Edit</a>
                                                    <form method="POST" action="{{ route('business.products.remove', [$business, $product]) }}" 
                                                          onsubmit="return confirm('Remove this product from the business?')" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">Remove</button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="px-6 py-8 text-center">
                        <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">No products assigned to this business yet.</p>
                        @if(auth()->user()->hasPermission('manage_businesses') && $availableProducts->count() > 0)
                            <a href="{{ route('business.products.create', $business) }}" 
                               class="mt-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-sm">
                                Assign first product
                            </a>
                        @endif
                    </div>
                @endif
            </div>
        </div>

        <!-- Available Products -->
        @if($availableProducts->count() > 0)
            <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                        Available Products ({{ $availableProducts->count() }})
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Products that can be assigned to this business</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                        @foreach($availableProducts as $product)
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex justify-between items-start mb-3">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ $product->name }}</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $product->category_label }}</p>
                                    </div>
                                    @if(auth()->user()->hasPermission('manage_businesses'))
                                        <a href="{{ route('business.products.create', $business) }}?product={{ $product->id }}" 
                                           class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-sm">
                                            Assign
                                        </a>
                                    @endif
                                </div>
                                
                                @if($product->description)
                                    <p class="text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mb-3">{{ Str::limit($product->description, 80) }}</p>
                                @endif
                                
                                <div class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 space-y-1">
                                    <div class="flex justify-between">
                                        <span>Base Price:</span>
                                        <span>{{ $product->formatted_base_price }}</span>
                                    </div>
                                    @if($product->pricing_model)
                                        <div class="flex justify-between">
                                            <span>Model:</span>
                                            <span>{{ $product->pricing_model_label }}</span>
                                        </div>
                                    @endif
                                </div>


                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif

        <!-- Product Statistics -->
        <div class="mt-8 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-700 rounded-md p-4 transition duration-150 ease-in-out">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $assignedProducts->count() }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Assigned Products</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $assignedProducts->where('pivot.status', 'active')->count() }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Active Products</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-yellow-600">{{ $assignedProducts->where('pivot.status', 'pending')->count() }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Pending Products</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $availableProducts->count() }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Available Products</div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
