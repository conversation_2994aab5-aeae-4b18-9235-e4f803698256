@extends('layouts.app')

@section('title', 'Create Tag')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Create Tag</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Create a new tag for organizing businesses</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('tags.index') }}" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Tags
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <form method="POST" action="{{ route('tags.store') }}" class="p-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Tag Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Tag Name <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('name') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">A unique name for this tag</p>
                    </div>

                    <!-- Tag Color -->
                    <div>
                        <label for="color" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Color <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <div class="mt-1 flex items-center space-x-3">
                            <input type="color" name="color" id="color" value="{{ old('color', '#3b82f6') }}" required
                                   class="h-10 w-20 border border-gray-300 dark:border-gray-600 rounded-md @error('color') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                            <div class="flex-1">
                                <input type="text" id="color-hex" value="{{ old('color', '#3b82f6') }}" readonly
                                       class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-700 text-sm transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                            </div>
                        </div>
                        @error('color')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Choose a color for this tag</p>
                    </div>

                    <!-- Predefined Colors -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quick Color Selection</label>
                        <div class="flex flex-wrap gap-2">
                            @foreach($predefinedColors as $hex => $name)
                                <button type="button" 
                                        class="w-8 h-8 rounded-full border-2 border-gray-300 dark:border-gray-600 hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                                        style="background-color: {{ $hex }};"
                                        onclick="selectColor('{{ $hex }}')"
                                        title="{{ $name }}">
                                </button>
                            @endforeach
                        </div>
                    </div>

                    <!-- Status -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                                   class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white">
                            <label for="is_active" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Active
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            Active tags can be assigned to businesses
                        </p>
                    </div>

                    <!-- Tag Preview -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Preview</label>
                        <div id="tag-preview" class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                             style="background-color: #3b82f620; color: #3b82f6;">
                            <span id="preview-text">Sample Tag</span>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                        <textarea name="description" id="description" rows="3"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('description') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            Optional description of what this tag represents (max 500 characters)
                        </p>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('tags.index') }}" 
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Create Tag
                    </button>
                </div>
            </form>
        </div>

        <!-- Tag Guidelines -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4 transition duration-150 ease-in-out">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Tag Guidelines</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc pl-5 space-y-1">
                            <li>Tag names should be descriptive and unique</li>
                            <li>Use consistent naming conventions (e.g., "Industry: Technology")</li>
                            <li>Choose colors that are visually distinct from existing tags</li>
                            <li>Consider creating tags for: industries, business sizes, locations, services, etc.</li>
                            <li>Tags help organize and filter businesses effectively</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const colorInput = document.getElementById('color');
    const colorHex = document.getElementById('color-hex');
    const nameInput = document.getElementById('name');
    const preview = document.getElementById('tag-preview');
    const previewText = document.getElementById('preview-text');

    // Update color hex display and preview
    colorInput.addEventListener('input', function() {
        const color = this.value;
        colorHex.value = color;
        updatePreview();
    });

    // Update preview when name changes
    nameInput.addEventListener('input', function() {
        updatePreview();
    });

    function updatePreview() {
        const color = colorInput.value;
        const name = nameInput.value || 'Sample Tag';
        
        preview.style.backgroundColor = color + '20';
        preview.style.color = color;
        previewText.textContent = name;
    }

    // Initialize preview
    updatePreview();
});

function selectColor(hex) {
    document.getElementById('color').value = hex;
    document.getElementById('color-hex').value = hex;
    
    // Trigger input event to update preview
    const event = new Event('input', { bubbles: true });
    document.getElementById('color').dispatchEvent(event);
}
</script>
@endsection
