@extends('layouts.app')

@section('title', 'Business Tags')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Tags: {{ $business->name }}</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Manage tags for this business</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('business.show', $business) }}" 
                   class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Business
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Assigned Tags -->
            <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                        Assigned Tags ({{ $assignedTags->count() }})
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Tags currently assigned to this business</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    @if($assignedTags->count() > 0)
                        <div class="p-6">
                            <div class="space-y-3">
                                @foreach($assignedTags as $tag)
                                    <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                                        <div class="flex items-center">
                                            <div class="w-4 h-4 rounded-full mr-3" style="background-color: {{ $tag->color }};"></div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $tag->name }}</p>
                                                @if($tag->description)
                                                    <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $tag->description }}</p>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                                Added {{ $tag->pivot->created_at->diffForHumans() }}
                                            </span>
                                            @if(auth()->user()->hasPermission('manage_businesses'))
                                                <form method="POST" action="{{ route('business.tags.remove', [$business, $tag]) }}" 
                                                      onsubmit="return confirm('Remove this tag from the business?')" class="inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:text-red-200 text-sm">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <div class="px-6 py-8 text-center">
                            <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">No tags assigned to this business yet.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Available Tags -->
            <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                        Available Tags ({{ $availableTags->count() }})
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Tags that can be assigned to this business</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    @if($availableTags->count() > 0)
                        <form method="POST" action="{{ route('business.tags.assign', $business) }}" class="p-6">
                            @csrf
                            <div class="space-y-3 mb-4">
                                @foreach($availableTags as $tag)
                                    <div class="flex items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 transition duration-150 ease-in-out">
                                        <input type="checkbox" name="tag_ids[]" value="{{ $tag->id }}" 
                                               id="tag_{{ $tag->id }}"
                                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <label for="tag_{{ $tag->id }}" class="ml-3 flex items-center cursor-pointer flex-1">
                                            <div class="w-4 h-4 rounded-full mr-3" style="background-color: {{ $tag->color }};"></div>
                                            <div>
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $tag->name }}</p>
                                                @if($tag->description)
                                                    <p class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $tag->description }}</p>
                                                @endif
                                            </div>
                                        </label>
                                    </div>
                                @endforeach
                            </div>

                            @if(auth()->user()->hasPermission('manage_businesses'))
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center space-x-2">
                                        <button type="button" onclick="selectAll()" 
                                                class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-sm">
                                            Select All
                                        </button>
                                        <span class="text-gray-300">|</span>
                                        <button type="button" onclick="selectNone()" 
                                                class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-sm">
                                            Select None
                                        </button>
                                    </div>
                                    <button type="submit" 
                                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                                        Assign Selected Tags
                                    </button>
                                </div>
                            @endif
                        </form>
                    @else
                        <div class="px-6 py-8 text-center">
                            <p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">All available tags are already assigned to this business.</p>
                            @if(auth()->user()->hasPermission('manage_businesses'))
                                <a href="{{ route('tags.create') }}" 
                                   class="mt-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-sm">
                                    Create new tag
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Tag Assignment -->
        @if(auth()->user()->hasPermission('manage_businesses'))
            <div class="mt-8 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Quick Tag Assignment</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Search and assign tags quickly</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex space-x-4">
                        <div class="flex-1">
                            <input type="text" id="tag-search" placeholder="Search tags..." 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-gray-700 dark:text-white">
                        </div>
                        <button type="button" onclick="searchTags()" 
                                class="bg-gray-50 dark:bg-gray-7000 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Search
                        </button>
                    </div>
                    <div id="search-results" class="mt-4 space-y-2"></div>
                </div>
            </div>
        @endif

        <!-- Tag Statistics -->
        <div class="mt-8 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-700 rounded-md p-4 transition duration-150 ease-in-out">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $assignedTags->count() }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Assigned Tags</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $availableTags->count() }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Available Tags</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $assignedTags->count() + $availableTags->count() }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Total Tags</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="tag_ids[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function selectNone() {
    const checkboxes = document.querySelectorAll('input[name="tag_ids[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}

function searchTags() {
    const query = document.getElementById('tag-search').value;
    const resultsContainer = document.getElementById('search-results');
    
    if (query.length < 2) {
        resultsContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-sm">Enter at least 2 characters to search</p>';
        return;
    }
    
    fetch(`{{ route('business.tags.search', $business) }}?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(tags => {
            if (tags.length === 0) {
                resultsContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-sm">No tags found</p>';
                return;
            }
            
            let html = '<div class="space-y-2">';
            tags.forEach(tag => {
                html += `
                    <div class="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-700 rounded">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${tag.color};"></div>
                            <span class="text-sm">${tag.name}</span>
                        </div>
                        <button type="button" onclick="assignTag(${tag.id}, '${tag.name}')" 
                                class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 text-sm">
                            Assign
                        </button>
                    </div>
                `;
            });
            html += '</div>';
            resultsContainer.innerHTML = html;
        })
        .catch(error => {
            resultsContainer.innerHTML = '<p class="text-red-500 dark:text-red-400 text-sm">Error searching tags</p>';
        });
}

function assignTag(tagId, tagName) {
    fetch(`{{ route('business.tags.bulk-assign', $business) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            tag_ids: [tagId]
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Refresh to show updated tags
        } else {
            alert('Error assigning tag');
        }
    })
    .catch(error => {
        alert('Error assigning tag');
    });
}

// Auto-search on input
document.getElementById('tag-search').addEventListener('input', function() {
    if (this.value.length >= 2) {
        searchTags();
    } else if (this.value.length === 0) {
        document.getElementById('search-results').innerHTML = '';
    }
});
</script>
@endsection
