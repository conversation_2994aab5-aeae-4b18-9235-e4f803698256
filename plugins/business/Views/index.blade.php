@extends('layouts.app')

@section('title', 'Business Management')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Business Management</h1>
        @if(auth()->user()->hasPermission('manage_businesses'))
            <div class="flex space-x-3">
                <a href="{{ route('business.import.index') }}"
                   class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 dark:bg-green-600 dark:hover:bg-green-500 dark:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-upload mr-2"></i>
                    Import Data
                </a>
                <a href="{{ route('business.create') }}"
                   class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-600 dark:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-plus mr-2"></i>
                    Add New Business
                </a>
            </div>
        @endif
    </div>

    <!-- Search and Filter Form -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 border border-gray-200 dark:border-gray-700 transition duration-150 ease-in-out">
        <form method="GET" action="{{ route('business.index') }}">
            <!-- Unified Search Box -->
            <div class="mb-6">
                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Businesses</label>
                <div class="relative">
                    <input type="text" name="search" id="search" value="{{ request('search') }}"
                           placeholder="Search by business name, email, Taqnyat ID, username, brand name, description, or location..."
                           class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"></i>
                    </div>
                </div>
            </div>

            <!-- Filter Checkboxes -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- Business Status Filter - Multi-Select Dropdown -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Business Status</h4>
                    <div class="relative">
                        <button type="button" id="status-dropdown-btn"
                                class="w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 dark:focus:border-blue-400 transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                            <span id="status-dropdown-text" class="text-sm text-gray-700 dark:text-gray-300">
                                @if(request('status') && count(request('status')) > 0)
                                    {{ count(request('status')) }} status(es) selected
                                @else
                                    Select status(es)
                                @endif
                            </span>
                            <i class="fas fa-chevron-down float-right mt-0.5 text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"></i>
                        </button>

                        <div id="status-dropdown-menu" class="hidden absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg transition duration-150 ease-in-out dark:bg-gray-700 dark:text-white">
                            <div class="py-1 max-h-60 overflow-y-auto">
                                @php $statuses = ['lead' => 'Lead', 'deal' => 'Deal', 'customer' => 'Customer', 'partner' => 'Partner', 'churned' => 'Churned', 'lost' => 'Lost']; @endphp
                                @foreach($statuses as $value => $label)
                                    <label class="flex items-center px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 cursor-pointer transition duration-150 ease-in-out">
                                        <input type="checkbox" name="status[]" value="{{ $value }}"
                                               {{ in_array($value, request('status', [])) ? 'checked' : '' }}
                                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700 dark:text-white dark:bg-gray-700 dark:text-white"
                                               onchange="updateStatusDropdownText()">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ $label }}</span>
                                        @if($value === 'lead')
                                            <span class="ml-auto text-xs text-blue-600 dark:text-blue-400">🔍</span>
                                        @elseif($value === 'deal')
                                            <span class="ml-auto text-xs text-orange-600 dark:text-orange-400">🤝</span>
                                        @elseif($value === 'customer')
                                            <span class="ml-auto text-xs text-green-600 dark:text-green-400">✅</span>
                                        @elseif($value === 'partner')
                                            <span class="ml-auto text-xs text-purple-600 dark:text-purple-400">🤝</span>
                                        @elseif($value === 'churned')
                                            <span class="ml-auto text-xs text-red-600 dark:text-red-400">❌</span>
                                        @elseif($value === 'lost')
                                            <span class="ml-auto text-xs text-gray-600 dark:text-gray-400 dark:text-gray-500">💔</span>
                                        @endif
                                    </label>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Filter - Multi-Select Dropdown -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Products</h4>
                    <div class="relative">
                        <button type="button" id="products-dropdown-btn"
                                class="w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400">
                            <span id="products-dropdown-text" class="text-sm text-gray-700 dark:text-gray-300">
                                @if(request('products') && count(request('products')) > 0)
                                    {{ count(request('products')) }} product(s) selected
                                @else
                                    Select product(s)
                                @endif
                            </span>
                            <i class="fas fa-chevron-down float-right mt-0.5 text-gray-400 dark:text-gray-500"></i>
                        </button>

                        <div id="products-dropdown-menu" class="hidden absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
                            <div class="py-1 max-h-60 overflow-y-auto">
                                @foreach(\Plugins\Business\Models\Product::orderBy('name')->get() as $product)
                                    <label class="flex items-center px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 cursor-pointer transition duration-150 ease-in-out">
                                        <input type="checkbox" name="products[]" value="{{ $product->id }}"
                                               {{ in_array($product->id, request('products', [])) ? 'checked' : '' }}
                                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                               onchange="updateProductsDropdownText()">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ $product->name }}</span>
                                    </label>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tags Filter - Multi-Select Dropdown -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Tags</h4>
                    <div class="relative">
                        <button type="button" id="tags-dropdown-btn"
                                class="w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400">
                            <span id="tags-dropdown-text" class="text-sm text-gray-700 dark:text-gray-300">
                                @if(request('tags') && count(request('tags')) > 0)
                                    {{ count(request('tags')) }} tag(s) selected
                                @else
                                    Select tag(s)
                                @endif
                            </span>
                            <i class="fas fa-chevron-down float-right mt-0.5 text-gray-400 dark:text-gray-500"></i>
                        </button>

                        <div id="tags-dropdown-menu" class="hidden absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
                            <div class="py-1 max-h-60 overflow-y-auto">
                                @foreach(\Plugins\Business\Models\Tag::orderBy('name')->get() as $tag)
                                    <label class="flex items-center px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 cursor-pointer transition duration-150 ease-in-out">
                                        <input type="checkbox" name="tags[]" value="{{ $tag->id }}"
                                               {{ in_array($tag->id, request('tags', [])) ? 'checked' : '' }}
                                               class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                               onchange="updateTagsDropdownText()">
                                        <div class="ml-2 flex items-center">
                                            <div class="w-3 h-3 rounded-full mr-2" style="background-color: {{ $tag->color }};"></div>
                                            <span class="text-sm text-gray-700 dark:text-gray-300">{{ $tag->name }}</span>
                                        </div>
                                    </label>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-2">
                <button type="submit" class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-search mr-2"></i>
                    Search
                </button>
                @if(request()->hasAny(['search', 'status', 'products', 'tags']))
                    <a href="{{ route('business.index') }}" class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-7000 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        <i class="fas fa-times mr-2"></i>
                        Clear Filters
                    </a>
                @endif
            </div>
        </form>
    </div>

    <!-- Businesses Table -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
        @if($businesses->count() > 0)
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Business</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Tags</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Products</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($businesses as $business)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 transition duration-150 ease-in-out">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <img class="h-10 w-10 rounded-full mr-3" src="{{ $business->logo_url }}" alt="{{ $business->name }}">
                                    <div>
                                        <div class="text-sm font-medium">
                                            <a href="{{ route('business.show', $business) }}" class="text-blue-600 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300 hover:underline">
                                                {{ $business->name }}
                                            </a>
                                        </div>
                                        @if($business->brand_name && $business->brand_name !== $business->name)
                                            <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500">{{ $business->brand_name }}</div>
                                        @endif
                                        @if($business->taqnyat_id)
                                            <div class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1" title="Taqnyat ID">
                                                <i class="fas fa-sms mr-1"></i>
                                                {{ $business->taqnyat_id }}
                                            </div>
                                        @endif
                                        @if($business->taqnyat_username)
                                            <div class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1" title="Taqnyat Username">
                                                <i class="fas fa-user mr-1"></i>
                                                {{ '@' . $business->taqnyat_username }}
                                            </div>
                                        @endif
                                        @if($business->website_url)
                                            <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                                <a href="{{ $business->website_url }}" target="_blank" class="hover:underline">
                                                    <i class="fas fa-external-link-alt mr-1"></i>Website
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-wrap gap-1">
                                    @php $businessTags = $business->tags()->limit(3)->get(); @endphp
                                    @foreach($businessTags as $tag)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                              style="background-color: {{ $tag->color }}20; color: {{ $tag->color }};">
                                            {{ $tag->name }}
                                        </span>
                                    @endforeach
                                    @if($business->tags()->count() > 3)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition duration-150 ease-in-out">
                                            +{{ $business->tags()->count() - 3 }}
                                        </span>
                                    @endif
                                    @if($business->tags()->count() == 0)
                                        <span class="text-xs text-gray-400 dark:text-gray-500">No tags</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-1">
                                    @php $businessProducts = $business->products->take(4); @endphp
                                    @if($businessProducts->count() > 0)
                                        @foreach($businessProducts as $product)
                                            <div class="flex items-center" title="{{ $product->name }}">
                                                <i class="{{ $product->icon ?? 'fas fa-box' }} text-gray-600 dark:text-gray-400 dark:text-gray-500 text-sm"></i>
                                            </div>
                                        @endforeach
                                        @if($business->products->count() > 4)
                                            <span class="text-xs text-gray-500 dark:text-gray-400 dark:text-gray-500 ml-1">+{{ $business->products->count() - 4 }}</span>
                                        @endif
                                    @else
                                        <span class="text-xs text-gray-400 dark:text-gray-500">No products</span>
                                    @endif
                                </div>
                            </td>

                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $business->status_color }}">
                                    {{ $business->status_label }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{{ route('business.show', $business) }}"
                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300" title="View Business">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if(auth()->user()->hasPermission('manage_businesses'))
                                        <a href="{{ route('business.edit', $business) }}"
                                           class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Edit Business">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ route('business.tags.index', $business) }}"
                                           class="text-purple-600 dark:text-purple-400 hover:text-purple-900" title="Manage Tags">
                                            <i class="fas fa-tags"></i>
                                        </a>
                                        <a href="{{ route('business.products.index', $business) }}"
                                           class="text-green-600 dark:text-green-400 hover:text-green-900" title="Manage Products">
                                            <i class="fas fa-box"></i>
                                        </a>
                                    @endif
                                    @if(auth()->user()->hasPermission('view_business_reports'))
                                        <a href="{{ route('business.reports', $business) }}"
                                           class="text-orange-600 dark:text-orange-400 hover:text-orange-900" title="Reports">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                {{ $businesses->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="text-gray-500 dark:text-gray-400 dark:text-gray-500 text-lg">No businesses found.</div>
                @if(auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('business.create') }}" 
                       class="mt-4 inline-block bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Create Your First Business
                    </a>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
// Multi-select dropdown functionality
document.addEventListener('DOMContentLoaded', function() {
    // Status dropdown
    const statusDropdownBtn = document.getElementById('status-dropdown-btn');
    const statusDropdownMenu = document.getElementById('status-dropdown-menu');

    if (statusDropdownBtn && statusDropdownMenu) {
        // Toggle dropdown
        statusDropdownBtn.addEventListener('click', function(e) {
            e.preventDefault();
            statusDropdownMenu.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!statusDropdownBtn.contains(e.target) && !statusDropdownMenu.contains(e.target)) {
                statusDropdownMenu.classList.add('hidden');
            }
        });

        // Prevent dropdown from closing when clicking inside
        statusDropdownMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Products dropdown
    const productsDropdownBtn = document.getElementById('products-dropdown-btn');
    const productsDropdownMenu = document.getElementById('products-dropdown-menu');

    if (productsDropdownBtn && productsDropdownMenu) {
        // Toggle dropdown
        productsDropdownBtn.addEventListener('click', function(e) {
            e.preventDefault();
            productsDropdownMenu.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!productsDropdownBtn.contains(e.target) && !productsDropdownMenu.contains(e.target)) {
                productsDropdownMenu.classList.add('hidden');
            }
        });

        // Prevent dropdown from closing when clicking inside
        productsDropdownMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }

    // Tags dropdown
    const tagsDropdownBtn = document.getElementById('tags-dropdown-btn');
    const tagsDropdownMenu = document.getElementById('tags-dropdown-menu');

    if (tagsDropdownBtn && tagsDropdownMenu) {
        // Toggle dropdown
        tagsDropdownBtn.addEventListener('click', function(e) {
            e.preventDefault();
            tagsDropdownMenu.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!tagsDropdownBtn.contains(e.target) && !tagsDropdownMenu.contains(e.target)) {
                tagsDropdownMenu.classList.add('hidden');
            }
        });

        // Prevent dropdown from closing when clicking inside
        tagsDropdownMenu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
});

// Update dropdown text based on selected checkboxes
function updateStatusDropdownText() {
    const checkboxes = document.querySelectorAll('input[name="status[]"]:checked');
    const dropdownText = document.getElementById('status-dropdown-text');

    if (checkboxes.length === 0) {
        dropdownText.textContent = 'Select status(es)';
    } else if (checkboxes.length === 1) {
        dropdownText.textContent = checkboxes[0].nextElementSibling.textContent.trim();
    } else {
        dropdownText.textContent = `${checkboxes.length} status(es) selected`;
    }
}

function updateProductsDropdownText() {
    const checkboxes = document.querySelectorAll('input[name="products[]"]:checked');
    const dropdownText = document.getElementById('products-dropdown-text');

    if (checkboxes.length === 0) {
        dropdownText.textContent = 'Select product(s)';
    } else if (checkboxes.length === 1) {
        dropdownText.textContent = checkboxes[0].nextElementSibling.textContent.trim();
    } else {
        dropdownText.textContent = `${checkboxes.length} product(s) selected`;
    }
}

function updateTagsDropdownText() {
    const checkboxes = document.querySelectorAll('input[name="tags[]"]:checked');
    const dropdownText = document.getElementById('tags-dropdown-text');

    if (checkboxes.length === 0) {
        dropdownText.textContent = 'Select tag(s)';
    } else if (checkboxes.length === 1) {
        dropdownText.textContent = checkboxes[0].nextElementSibling.textContent.trim();
    } else {
        dropdownText.textContent = `${checkboxes.length} tag(s) selected`;
    }
}

// Initialize dropdown text on page load
document.addEventListener('DOMContentLoaded', function() {
    updateStatusDropdownText();
    updateProductsDropdownText();
    updateTagsDropdownText();
});
</script>
@endpush
