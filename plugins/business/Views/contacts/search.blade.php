@extends('layouts.app')

@section('title', 'Add Existing Contact')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Add Existing Contact</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Search and add existing contacts to {{ $business->name }}</p>
            </div>
            <a href="{{ route('business.contacts.index', $business) }}"
               class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Back to Contacts
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <div class="p-6">
                <!-- Search Section -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Search for Contacts</h3>
                    
                    <!-- Search Input -->
                    <div class="mb-4">
                        <label for="contact_search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Search for Contact
                        </label>
                        <div class="relative">
                            <input type="text" 
                                   id="contact_search" 
                                   placeholder="Type contact name, email, phone, or position..."
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Search by name, Arabic name, email, phone number, position, or department
                        </p>
                    </div>

                    <!-- Search Results -->
                    <div id="search_results" class="hidden mb-4">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Search Results</h4>
                        <div class="border border-gray-300 dark:border-gray-600 rounded-md max-h-96 overflow-y-auto">
                            <!-- Results will be populated here -->
                        </div>
                    </div>

                    <!-- No Results Message -->
                    <div id="no_results" class="hidden mb-4">
                        <div class="text-center py-8">
                            <i class="fas fa-search text-gray-400 text-3xl mb-3"></i>
                            <p class="text-gray-500 dark:text-gray-400">No contacts found matching your search.</p>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">How to add contacts</h3>
                            <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                                <ul class="list-disc list-inside space-y-1">
                                    <li><strong>Copy:</strong> Creates a duplicate of the contact for this business (original remains unchanged)</li>
                                    <li><strong>Move:</strong> Transfers the contact from their current business to this business</li>
                                    <li>Only contacts from other businesses will appear in search results</li>
                                    <li>You can also <a href="{{ route('business.contacts.create', $business) }}" class="underline hover:text-blue-600">create a new contact</a> if the person doesn't exist in the system</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Copy/Move Modal -->
<div id="actionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="modal_title">Add Contact</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div id="modal_content" class="mb-4">
                <!-- Contact details will be populated here -->
            </div>
            
            <form id="actionForm" method="POST" action="{{ route('business.contacts.copy-contact', $business) }}">
                @csrf
                <input type="hidden" name="contact_id" id="selected_contact_id">
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Action</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="copy_mode" value="copy" checked class="mr-2">
                            <span class="text-sm text-gray-900 dark:text-white">
                                <strong>Copy</strong> - Create a duplicate contact for this business
                            </span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="copy_mode" value="move" class="mr-2">
                            <span class="text-sm text-gray-900 dark:text-white">
                                <strong>Move</strong> - Transfer contact to this business
                            </span>
                        </label>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal()" 
                            class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Add Contact
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('contact_search');
    const searchResults = document.getElementById('search_results');
    const noResults = document.getElementById('no_results');
    const modal = document.getElementById('actionModal');
    
    let searchTimeout;

    // Handle search input
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        
        clearTimeout(searchTimeout);
        
        if (query.length < 2) {
            searchResults.classList.add('hidden');
            noResults.classList.add('hidden');
            return;
        }

        searchTimeout = setTimeout(() => {
            searchContacts(query);
        }, 300);
    });

    function searchContacts(query) {
        fetch(`{{ route('business.contacts.search-contacts', $business) }}?search=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data.contacts);
            })
            .catch(error => {
                console.error('Search error:', error);
            });
    }

    function displaySearchResults(contacts) {
        if (contacts.length === 0) {
            searchResults.classList.add('hidden');
            noResults.classList.remove('hidden');
            return;
        }

        noResults.classList.add('hidden');
        
        const resultsHtml = contacts.map(contact => `
            <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0" 
                 onclick="showActionModal(${contact.id}, '${contact.name}', '${contact.arabic_name || ''}', '${contact.email || ''}', '${contact.contact_info || ''}', '${contact.position || ''}', '${contact.department || ''}', '${contact.business_name}')">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="font-medium text-gray-900 dark:text-white">
                            ${contact.display_name}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            ${contact.position ? contact.position : 'No position'}
                            ${contact.department ? ` - ${contact.department}` : ''}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            ${contact.contact_info || 'No contact info'}
                        </div>
                        <div class="text-xs text-gray-400 dark:text-gray-500">
                            Currently at: ${contact.business_name}
                        </div>
                    </div>
                    <div class="text-blue-600 dark:text-blue-400">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                </div>
            </div>
        `).join('');

        searchResults.querySelector('div:last-child').innerHTML = resultsHtml;
        searchResults.classList.remove('hidden');
    }

    window.showActionModal = function(id, name, arabicName, email, contactInfo, position, department, businessName) {
        document.getElementById('selected_contact_id').value = id;
        document.getElementById('modal_title').textContent = `Add ${name}`;
        
        const modalContent = `
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-4">
                <div class="font-medium text-gray-900 dark:text-white">${name}${arabicName ? ` (${arabicName})` : ''}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">${position || 'No position'}${department ? ` - ${department}` : ''}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">${contactInfo || 'No contact info'}</div>
                <div class="text-xs text-gray-400 dark:text-gray-500">Currently at: ${businessName}</div>
            </div>
        `;
        
        document.getElementById('modal_content').innerHTML = modalContent;
        modal.classList.remove('hidden');
    };

    window.closeModal = function() {
        modal.classList.add('hidden');
    };

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
});
</script>
@endsection
