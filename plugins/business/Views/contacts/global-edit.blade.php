@extends('layouts.app')

@section('title', 'Edit Contact')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Contact</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Edit contact details
                    @if($contact->business)
                        for {{ $contact->business->name }}
                    @endif
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('contacts.index') }}"
                   class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Contacts
                </a>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <form method="POST" action="{{ route('contacts.update', $contact) }}" class="p-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Contact Information</h3>
                    </div>

                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Full Name <span class="text-red-500 dark:text-red-400">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name', $contact->name) }}" required
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('name') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Position -->
                    <div>
                        <label for="position" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Position/Title</label>
                        <input type="text" name="position" id="position" value="{{ old('position', $contact->position) }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('position') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        @error('position')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Department -->
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Department</label>
                        <input type="text" name="department" id="department" value="{{ old('department', $contact->department) }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('department') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        @error('department')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email Address</label>
                        <input type="email" name="email" id="email" value="{{ old('email', $contact->email) }}"
                               class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('email') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        @error('email')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Primary Phone Number</label>
                        <div class="flex space-x-2">
                            <input type="tel" name="phone" id="phone" value="{{ old('phone', $contact->phone) }}"
                                   placeholder="(*************"
                                   class="flex-1 mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('phone') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                            <input type="text" name="phone_ext" id="phone_ext" value="{{ old('phone_ext', $contact->phone_ext) }}"
                                   placeholder="Ext."
                                   class="w-20 mt-1 block border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('phone_ext') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        </div>
                        @error('phone')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        @error('phone_ext')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Second Phone -->
                    <div>
                        <label for="phone2" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Second Phone Number</label>
                        <div class="flex space-x-2">
                            <input type="tel" name="phone2" id="phone2" value="{{ old('phone2', $contact->phone2) }}"
                                   placeholder="(*************"
                                   class="flex-1 mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('phone2') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                            <input type="text" name="phone2_ext" id="phone2_ext" value="{{ old('phone2_ext', $contact->phone2_ext) }}"
                                   placeholder="Ext."
                                   class="w-20 mt-1 block border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('phone2_ext') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">
                        </div>
                        @error('phone2')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        @error('phone2_ext')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Primary Contact -->
                    @if($contact->business)
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_primary" id="is_primary" value="1" {{ old('is_primary', $contact->is_primary) ? 'checked' : '' }}
                                   class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-700">
                            <label for="is_primary" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Set as primary contact
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Primary contacts are highlighted and used as the main point of contact for this business.
                        </p>
                    </div>
                    @endif

                    <!-- Notes -->
                    <div class="md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes</label>
                        <textarea name="notes" id="notes" rows="3"
                                  class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 @error('notes') border-red-300 dark:border-red-600 @enderror dark:bg-gray-700 dark:text-white">{{ old('notes', $contact->notes) }}</textarea>
                        @error('notes')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Additional information about this contact (optional).
                        </p>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('contacts.index') }}"
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Update Contact
                    </button>
                </div>
            </form>
        </div>

        <!-- Contact History -->
        <div class="mt-6 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-700 rounded-md p-4 transition duration-150 ease-in-out">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-gray-400 dark:text-gray-500"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-gray-800 dark:text-gray-200">Contact History</h3>
                    <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        <p>Created: {{ $contact->created_at->format('M d, Y \a\t g:i A') }}</p>
                        @if($contact->updated_at != $contact->created_at)
                            <p>Last updated: {{ $contact->updated_at->format('M d, Y \a\t g:i A') }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Phone number formatting function
function formatPhoneNumber(input) {
    let value = input.value.replace(/\D/g, '');
    if (value.length >= 6) {
        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    } else if (value.length >= 3) {
        value = value.replace(/(\d{3})(\d{3})/, '($1) $2');
    }
    input.value = value;
}

// Apply formatting to both phone fields
document.getElementById('phone').addEventListener('input', function(e) {
    formatPhoneNumber(e.target);
});

document.getElementById('phone2').addEventListener('input', function(e) {
    formatPhoneNumber(e.target);
});
</script>
@endsection
