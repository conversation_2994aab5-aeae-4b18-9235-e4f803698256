@extends('layouts.app')

@section('title', 'Contact Details')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ $contact->name }}</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Contact details
                    @if($contact->business)
                        for <a href="{{ route('business.show', $contact->business) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800">{{ $contact->business->name }}</a>
                    @endif
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('contacts.index') }}"
                   class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Contacts
                </a>
                @if(auth()->user()->hasPermission('manage_businesses'))
                    <a href="{{ route('contacts.edit', $contact) }}" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Edit Contact
                    </a>
                @endif
            </div>
        </div>

        <!-- Business Information -->
        @if($contact->business)
            <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out mb-6">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Business Information</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">The business this contact belongs to.</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700">
                    <dl>
                        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Business Name</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                <a href="{{ route('business.show', $contact->business) }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800">
                                    {{ $contact->business->name }}
                                </a>
                            </dd>
                        </div>
                        @if($contact->business->arabic_name)
                            <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Arabic Name</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                    {{ $contact->business->arabic_name }}
                                </dd>
                            </div>
                        @endif
                    </dl>
                </div>
            </div>
        @endif

        <!-- Contact Information -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Contact Information</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Personal details and contact information.</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                <dl>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Full name</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            {{ $contact->name }}
                            @if($contact->is_primary)
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                    Primary Contact
                                </span>
                            @endif
                        </dd>
                    </div>
                    @if($contact->arabic_name)
                        <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Arabic name</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                {{ $contact->arabic_name }}
                            </dd>
                        </div>
                    @endif
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Position</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            {{ $contact->position ?: 'Not specified' }}
                        </dd>
                    </div>
                    <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Department</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            {{ $contact->department ?: 'Not specified' }}
                        </dd>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email address</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            @if($contact->email)
                                <a href="mailto:{{ $contact->email }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                    {{ $contact->email }}
                                </a>
                            @else
                                <span class="text-gray-500 dark:text-gray-400">Not provided</span>
                            @endif
                        </dd>
                    </div>
                    <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Primary Phone</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                            @if($contact->phone)
                                <a href="tel:{{ $contact->phone }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                    {{ $contact->formatted_phone }}
                                </a>
                            @else
                                <span class="text-gray-500 dark:text-gray-400">Not provided</span>
                            @endif
                        </dd>
                    </div>
                    @if($contact->phone2)
                        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Secondary Phone</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                <a href="tel:{{ $contact->phone2 }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                    {{ $contact->formatted_phone2 }}
                                </a>
                            </dd>
                        </div>
                    @endif
                    @if($contact->notes)
                        <div class="bg-white dark:bg-gray-800 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6 transition duration-150 ease-in-out">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Notes</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white sm:mt-0 sm:col-span-2">
                                {{ $contact->notes }}
                            </dd>
                        </div>
                    @endif
                </dl>
            </div>
        </div>

        <!-- Contact Actions -->
        @if($contact->email || $contact->phone || $contact->phone2)
            <div class="mt-6 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Quick Actions</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Contact this person directly.</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                    <div class="flex flex-wrap gap-4">
                        @if($contact->email)
                            <a href="mailto:{{ $contact->email }}"
                               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:hover:bg-blue-600 transition duration-150 ease-in-out">
                                <i class="fas fa-envelope mr-2"></i>
                                Send Email
                            </a>
                        @endif
                        @if($contact->phone)
                            <a href="tel:{{ $contact->phone }}"
                               class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                                <i class="fas fa-phone mr-2"></i>
                                Call {{ $contact->formatted_phone }}
                            </a>
                        @endif
                        @if($contact->phone2)
                            <a href="tel:{{ $contact->phone2 }}"
                               class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                                <i class="fas fa-mobile-alt mr-2"></i>
                                Call {{ $contact->formatted_phone2 }}
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        @endif

        <!-- Contact Status -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Contact Status</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Information about this contact's completeness and status.</p>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold {{ $contact->isComplete() ? 'text-green-600 dark:text-green-400' : 'text-yellow-600' }}">
                            {{ $contact->isComplete() ? 'Complete' : 'Incomplete' }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Profile Status</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold {{ $contact->is_primary ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400' }}">
                            {{ $contact->is_primary ? 'Primary' : 'Secondary' }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Contact Type</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-600 dark:text-gray-400">
                            {{ $contact->created_at->diffForHumans() }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">Added</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Management Actions -->
        @if(auth()->user()->hasPermission('manage_businesses'))
            <div class="mt-6 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">Management Actions</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Administrative actions for this contact.</p>
                </div>
                <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:px-6">
                    <div class="flex space-x-4">
                        <form method="POST" action="{{ route('contacts.destroy', $contact) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this contact? This action cannot be undone.')" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 text-sm font-medium rounded-md text-red-700 bg-white dark:bg-gray-800 hover:bg-red-50 transition duration-150 ease-in-out">
                                <i class="fas fa-trash mr-2"></i>
                                Delete Contact
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        @endif

        <!-- Timestamps -->
        <div class="mt-6 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-700 rounded-md p-4 transition duration-150 ease-in-out">
            <div class="text-sm text-gray-600 dark:text-gray-400">
                <p><strong>Created:</strong> {{ $contact->created_at->format('M d, Y \a\t g:i A') }}</p>
                @if($contact->updated_at != $contact->created_at)
                    <p><strong>Last updated:</strong> {{ $contact->updated_at->format('M d, Y \a\t g:i A') }}</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
