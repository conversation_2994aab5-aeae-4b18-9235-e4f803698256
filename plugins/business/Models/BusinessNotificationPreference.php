<?php

namespace Plugins\Business\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use Carbon\Carbon;

class BusinessNotificationPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'business_id',
        'email_enabled',
        'sms_enabled',
        'whatsapp_enabled',
        'in_app_enabled',
        'frequency',
        'activity_types',
        'min_severity',
        'notification_email',
        'notification_phone',
        'notification_whatsapp',
        'quiet_hours_start',
        'quiet_hours_end',
        'quiet_days',
        'last_notification_sent',
    ];

    protected $casts = [
        'email_enabled' => 'boolean',
        'sms_enabled' => 'boolean',
        'whatsapp_enabled' => 'boolean',
        'in_app_enabled' => 'boolean',
        'activity_types' => 'array',
        'quiet_days' => 'array',
        'quiet_hours_start' => 'datetime:H:i',
        'quiet_hours_end' => 'datetime:H:i',
        'last_notification_sent' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user these preferences belong to
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the business these preferences are for
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get default notification preferences
     */
    public static function getDefaults(): array
    {
        return [
            'email_enabled' => true,
            'sms_enabled' => false,
            'whatsapp_enabled' => false,
            'in_app_enabled' => true,
            'frequency' => 'immediate',
            'activity_types' => [
                'comment',
                'document_upload',
                'status_change',
                'tag_assignment',
                'product_assignment',
                'user_assignment',
                'mention'
            ],
            'min_severity' => 'info',
            'quiet_hours_start' => null,
            'quiet_hours_end' => null,
            'quiet_days' => [],
        ];
    }

    /**
     * Get or create preferences for a user and business
     */
    public static function getForUserAndBusiness(User $user, Business $business): self
    {
        return static::firstOrCreate(
            [
                'user_id' => $user->id,
                'business_id' => $business->id,
            ],
            array_merge(static::getDefaults(), [
                'notification_email' => $user->email,
            ])
        );
    }

    /**
     * Check if notifications should be sent for an activity type
     */
    public function shouldNotifyForActivityType(string $activityType): bool
    {
        $enabledTypes = $this->activity_types ?? [];
        return in_array($activityType, $enabledTypes);
    }

    /**
     * Check if notifications should be sent for a severity level
     */
    public function shouldNotifyForSeverity(string $severity): bool
    {
        $severityLevels = ['info', 'warning', 'important', 'critical'];
        $minIndex = array_search($this->min_severity, $severityLevels);
        $activityIndex = array_search($severity, $severityLevels);
        
        return $activityIndex >= $minIndex;
    }

    /**
     * Check if we're in quiet hours
     */
    public function isInQuietHours(): bool
    {
        if (!$this->quiet_hours_start || !$this->quiet_hours_end) {
            return false;
        }

        $now = Carbon::now();
        $start = Carbon::createFromTimeString($this->quiet_hours_start);
        $end = Carbon::createFromTimeString($this->quiet_hours_end);

        // Handle overnight quiet hours (e.g., 22:00 to 06:00)
        if ($start->greaterThan($end)) {
            return $now->greaterThanOrEqualTo($start) || $now->lessThanOrEqualTo($end);
        }

        return $now->between($start, $end);
    }

    /**
     * Check if today is a quiet day
     */
    public function isQuietDay(): bool
    {
        if (empty($this->quiet_days)) {
            return false;
        }

        $today = Carbon::now()->dayOfWeek; // 0 = Sunday, 6 = Saturday
        return in_array($today, $this->quiet_days);
    }

    /**
     * Check if we should send notifications now
     */
    public function shouldSendNotification(): bool
    {
        // Check if notifications are disabled
        if (!$this->hasAnyChannelEnabled()) {
            return false;
        }

        // Check quiet hours and days
        if ($this->isInQuietHours() || $this->isQuietDay()) {
            return false;
        }

        // Check frequency limits
        return $this->canSendBasedOnFrequency();
    }

    /**
     * Check if any notification channel is enabled
     */
    public function hasAnyChannelEnabled(): bool
    {
        return $this->email_enabled || 
               $this->sms_enabled || 
               $this->whatsapp_enabled || 
               $this->in_app_enabled;
    }

    /**
     * Check if we can send based on frequency settings
     */
    protected function canSendBasedOnFrequency(): bool
    {
        if ($this->frequency === 'never') {
            return false;
        }

        if ($this->frequency === 'immediate') {
            return true;
        }

        if (!$this->last_notification_sent) {
            return true;
        }

        $lastSent = $this->last_notification_sent;
        $now = Carbon::now();

        switch ($this->frequency) {
            case 'hourly':
                return $now->diffInHours($lastSent) >= 1;
            case 'daily':
                return $now->diffInDays($lastSent) >= 1;
            case 'weekly':
                return $now->diffInWeeks($lastSent) >= 1;
            default:
                return true;
        }
    }

    /**
     * Update last notification sent timestamp
     */
    public function markNotificationSent(): void
    {
        $this->update(['last_notification_sent' => now()]);
    }

    /**
     * Get enabled notification channels
     */
    public function getEnabledChannels(): array
    {
        $channels = [];

        if ($this->email_enabled && $this->notification_email) {
            $channels['email'] = $this->notification_email;
        }

        if ($this->sms_enabled && $this->notification_phone) {
            $channels['sms'] = $this->notification_phone;
        }

        if ($this->whatsapp_enabled && $this->notification_whatsapp) {
            $channels['whatsapp'] = $this->notification_whatsapp;
        }

        if ($this->in_app_enabled) {
            $channels['in_app'] = true;
        }

        return $channels;
    }

    /**
     * Get frequency options
     */
    public static function getFrequencyOptions(): array
    {
        return [
            'immediate' => 'Immediate',
            'hourly' => 'Hourly digest',
            'daily' => 'Daily digest',
            'weekly' => 'Weekly digest',
            'never' => 'Never',
        ];
    }

    /**
     * Get severity options
     */
    public static function getSeverityOptions(): array
    {
        return [
            'info' => 'All activities',
            'warning' => 'Warning and above',
            'important' => 'Important and above',
            'critical' => 'Critical only',
        ];
    }
}
