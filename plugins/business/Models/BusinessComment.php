<?php

namespace Plugins\Business\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\User;

class BusinessComment extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'user_id',
        'activity_id',
        'content',
        'content_html',
        'mentions',
        'is_edited',
        'edited_at',
        'parent_id',
        'is_visible',
    ];

    protected $casts = [
        'mentions' => 'array',
        'is_edited' => 'boolean',
        'is_visible' => 'boolean',
        'edited_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the business this comment belongs to
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the user who wrote this comment
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the activity this comment belongs to
     */
    public function activity(): BelongsTo
    {
        return $this->belongsTo(BusinessActivity::class, 'activity_id');
    }

    /**
     * Get the parent comment (for replies)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(BusinessComment::class, 'parent_id');
    }

    /**
     * Get child comments (replies)
     */
    public function replies(): HasMany
    {
        return $this->hasMany(BusinessComment::class, 'parent_id');
    }

    /**
     * Get mentioned users
     */
    public function mentionedUsers()
    {
        if (empty($this->mentions)) {
            return collect();
        }

        return User::whereIn('id', $this->mentions)->get();
    }

    /**
     * Scope for visible comments
     */
    public function scopeVisible($query)
    {
        return $query->where('is_visible', true);
    }

    /**
     * Scope for top-level comments (not replies)
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Process content and extract mentions
     */
    public static function processContent(string $content): array
    {
        // Convert markdown-style formatting to HTML
        $html = static::convertToHtml($content);

        // Extract mentions (@username)
        $mentions = static::extractMentions($content);

        return [
            'content' => $content,
            'content_html' => $html,
            'mentions' => $mentions,
        ];
    }

    /**
     * Convert basic markdown to HTML
     */
    protected static function convertToHtml(string $content): string
    {
        // Basic markdown conversion
        $html = $content;
        
        // Bold text
        $html = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $html);
        
        // Italic text
        $html = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $html);
        
        // Links
        $html = preg_replace('/\[([^\]]+)\]\(([^)]+)\)/', '<a href="$2" target="_blank" class="text-blue-600 hover:text-blue-800">$1</a>', $html);
        
        // Auto-link URLs
        $html = preg_replace('/(https?:\/\/[^\s]+)/', '<a href="$1" target="_blank" class="text-blue-600 hover:text-blue-800">$1</a>', $html);
        
        // Convert newlines to <br>
        $html = nl2br($html);
        
        // Process mentions
        $html = preg_replace_callback('/@(\w+)/', function ($matches) {
            $username = $matches[1];
            $user = User::where('name', 'LIKE', "%{$username}%")->first();
            if ($user) {
                return '<span class="mention bg-blue-100 text-blue-800 px-1 rounded">@' . $user->name . '</span>';
            }
            return $matches[0];
        }, $html);
        
        return $html;
    }

    /**
     * Extract mentioned user IDs from content
     */
    protected static function extractMentions(string $content): array
    {
        preg_match_all('/@(\w+)/', $content, $matches);
        
        if (empty($matches[1])) {
            return [];
        }
        
        $usernames = $matches[1];
        $users = User::whereIn('name', $usernames)->get();
        
        return $users->pluck('id')->toArray();
    }

    /**
     * Mark comment as edited
     */
    public function markAsEdited(): void
    {
        $this->update([
            'is_edited' => true,
            'edited_at' => now(),
        ]);
    }

    /**
     * Create a comment and associated activity
     */
    public static function createWithActivity(array $data): self
    {
        $comment = static::create($data);
        
        // Create associated activity if not linked to existing activity
        if (!$comment->activity_id) {
            $activity = BusinessActivity::createActivity([
                'business_id' => $comment->business_id,
                'user_id' => $comment->user_id,
                'type' => 'comment',
                'category' => 'communication',
                'severity' => 'info',
                'title' => 'New comment added',
                'description' => substr($comment->content, 0, 100) . (strlen($comment->content) > 100 ? '...' : ''),
                'subject_type' => BusinessComment::class,
                'subject_id' => $comment->id,
            ]);
            
            $comment->update(['activity_id' => $activity->id]);
        }
        
        return $comment;
    }

    /**
     * Get formatted creation time
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get formatted edited time
     */
    public function getFormattedEditedAtAttribute(): string
    {
        return $this->edited_at ? $this->edited_at->diffForHumans() : '';
    }

    /**
     * Check if user can edit this comment
     */
    public function canEdit(User $user): bool
    {
        // User can edit their own comments
        if ($this->user_id === $user->id) {
            return true;
        }
        
        // Users with manage_businesses permission can edit any comment
        return $user->hasPermission('manage_businesses');
    }

    /**
     * Check if user can delete this comment
     */
    public function canDelete(User $user): bool
    {
        // User can delete their own comments
        if ($this->user_id === $user->id) {
            return true;
        }
        
        // Users with manage_businesses permission can delete any comment
        return $user->hasPermission('manage_businesses');
    }
}
