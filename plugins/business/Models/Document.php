<?php

namespace Plugins\Business\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Models\User;

class Document extends Model
{
    use HasFactory;

    protected $table = 'business_documents';

    protected $fillable = [
        'business_id',
        'document_type',
        'file_name',
        'original_name',
        'file_path',
        'mime_type',
        'file_size',
        'description',
        'uploaded_by',
        'upload_date',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'upload_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Document type labels
     */
    public static function getDocumentTypes(): array
    {
        return [
            'invoice' => 'Invoice',
            'quotation' => 'Quotation',
            'payment' => 'Payment',
            'receipt' => 'Receipt',
            'commercial_registration' => 'Commercial Registration (CR)',
            'tax_certificate' => 'Tax Certificate',
            'address_proof' => 'Address Proof',
            'license' => 'License',
            'contract' => 'Contract',
            'other' => 'Other',
        ];
    }

    /**
     * Get the business that owns this document
     */
    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the user who uploaded this document
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get document type label
     */
    public function getDocumentTypeLabelAttribute(): string
    {
        return static::getDocumentTypes()[$this->document_type] ?? $this->document_type;
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        
        if ($bytes >= **********) {
            return number_format($bytes / **********, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Check if file exists
     */
    public function fileExists(): bool
    {
        return Storage::disk('local')->exists($this->file_path);
    }

    /**
     * Get file URL for download
     */
    public function getDownloadUrlAttribute(): string
    {
        return route('business.documents.download', [$this->business_id, $this->id]);
    }

    /**
     * Check if document is an image
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if document is a PDF
     */
    public function isPdf(): bool
    {
        return $this->mime_type === 'application/pdf';
    }

    /**
     * Check if document can be viewed in browser
     */
    public function isViewableInBrowser(): bool
    {
        return $this->isImage() || $this->isPdf();
    }

    /**
     * Get file icon based on mime type
     */
    public function getFileIconAttribute(): string
    {
        if ($this->isImage()) {
            return 'fas fa-image';
        } elseif ($this->isPdf()) {
            return 'fas fa-file-pdf';
        } elseif (str_contains($this->mime_type, 'word')) {
            return 'fas fa-file-word';
        } elseif (str_contains($this->mime_type, 'excel') || str_contains($this->mime_type, 'spreadsheet')) {
            return 'fas fa-file-excel';
        } elseif (str_contains($this->mime_type, 'powerpoint') || str_contains($this->mime_type, 'presentation')) {
            return 'fas fa-file-powerpoint';
        } else {
            return 'fas fa-file';
        }
    }

    /**
     * Delete file from storage when model is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($document) {
            if ($document->fileExists()) {
                Storage::disk('local')->delete($document->file_path);
            }
        });
    }

    /**
     * Scope to filter by document type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('document_type', $type);
    }

    /**
     * Scope to get recent documents
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('upload_date', '>=', now()->subDays($days));
    }
}
