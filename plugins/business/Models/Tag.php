<?php

namespace Plugins\Business\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Models\User;

class Tag extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'color',
        'description',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tag) {
            if (empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
            }
        });

        static::updating(function ($tag) {
            if ($tag->isDirty('name') && empty($tag->slug)) {
                $tag->slug = Str::slug($tag->name);
            }
        });
    }

    /**
     * Get the user who created this tag
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the businesses that have this tag
     */
    public function businesses()
    {
        return $this->belongsToMany(Business::class, 'business_tags')
                    ->withPivot('assigned_by')
                    ->withTimestamps();
    }

    /**
     * Get users who assigned this tag to businesses
     */
    public function assigners()
    {
        return $this->belongsToMany(User::class, 'business_tags', 'tag_id', 'assigned_by')
                    ->withTimestamps();
    }

    /**
     * Scope to get active tags
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to search tags by name
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
    }

    /**
     * Get tag with business count
     */
    public function scopeWithBusinessCount($query)
    {
        return $query->withCount('businesses');
    }

    /**
     * Get popular tags (most used)
     */
    public function scopePopular($query, int $limit = 10)
    {
        return $query->withCount('businesses')
                    ->orderBy('businesses_count', 'desc')
                    ->limit($limit);
    }

    /**
     * Get tag badge HTML
     */
    public function getBadgeHtmlAttribute(): string
    {
        return sprintf(
            '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: %s20; color: %s;">%s</span>',
            $this->color,
            $this->color,
            htmlspecialchars($this->name)
        );
    }

    /**
     * Get predefined colors for tags
     */
    public static function getPredefinedColors(): array
    {
        return [
            '#3b82f6' => 'Blue',
            '#ef4444' => 'Red',
            '#10b981' => 'Green',
            '#f59e0b' => 'Yellow',
            '#8b5cf6' => 'Purple',
            '#06b6d4' => 'Cyan',
            '#f97316' => 'Orange',
            '#84cc16' => 'Lime',
            '#ec4899' => 'Pink',
            '#6b7280' => 'Gray',
        ];
    }

    /**
     * Ensure unique slug
     */
    public function ensureUniqueSlug(): void
    {
        $originalSlug = $this->slug;
        $counter = 1;

        while (static::where('slug', $this->slug)->where('id', '!=', $this->id)->exists()) {
            $this->slug = $originalSlug . '-' . $counter;
            $counter++;
        }
    }

    /**
     * Get usage statistics
     */
    public function getUsageStats(): array
    {
        return [
            'total_businesses' => $this->businesses()->count(),
            'active_businesses' => $this->businesses()->where('is_active', true)->count(),
            'recent_assignments' => $this->businesses()
                                        ->wherePivot('created_at', '>=', now()->subDays(30))
                                        ->count(),
        ];
    }
}
