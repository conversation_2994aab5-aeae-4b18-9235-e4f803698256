<?php

namespace Plugins\Business\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use App\Models\User;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'icon',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });

        static::updating(function ($product) {
            if ($product->isDirty('name') && empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });
    }

    /**
     * Get available product icons
     */
    public static function getAvailableIcons(): array
    {
        return [
            // Communication & Messaging
            'fab fa-whatsapp' => 'WhatsApp Business',
            'fas fa-comments' => 'Chat/Messaging',
            'fas fa-robot' => 'Chatbot',
            'fas fa-headset' => 'Live Chat Support',
            'fas fa-brain' => 'AI Chatbot',
            'fas fa-phone' => 'Call Bot/Phone Services',
            'fas fa-microphone' => 'Voice Services',
            'fas fa-envelope' => 'Email Marketing',
            'fas fa-sms' => 'SMS Services',

            // Technology & Software
            'fas fa-laptop' => 'Software/Applications',
            'fas fa-mobile-alt' => 'Mobile Apps',
            'fas fa-desktop' => 'Desktop Software',
            'fas fa-code' => 'Development Services',
            'fas fa-database' => 'Database Services',
            'fas fa-server' => 'Server/Hosting',
            'fas fa-cloud' => 'Cloud Services',
            'fas fa-wifi' => 'Network Services',

            // Business & Analytics
            'fas fa-chart-line' => 'Analytics/Reporting',
            'fas fa-chart-pie' => 'Business Intelligence',
            'fas fa-calculator' => 'Financial Services',
            'fas fa-credit-card' => 'Payment Processing',
            'fas fa-shopping-cart' => 'E-commerce',
            'fas fa-store' => 'Retail Solutions',
            'fas fa-warehouse' => 'Inventory Management',

            // Security & Compliance
            'fas fa-shield-alt' => 'Security Services',
            'fas fa-lock' => 'Data Protection',
            'fas fa-user-shield' => 'Identity Management',
            'fas fa-key' => 'Access Control',

            // Marketing & Design
            'fas fa-bullhorn' => 'Marketing/Advertising',
            'fas fa-paint-brush' => 'Design Services',
            'fas fa-camera' => 'Photography/Media',
            'fas fa-video' => 'Video Services',
            'fas fa-globe' => 'Web Services',

            // Professional Services
            'fas fa-briefcase' => 'Business Consulting',
            'fas fa-handshake' => 'Partnership Services',
            'fas fa-users' => 'Team/HR Services',
            'fas fa-graduation-cap' => 'Training/Education',
            'fas fa-tools' => 'Technical Support',
            'fas fa-wrench' => 'Maintenance Services',
            'fas fa-cog' => 'Configuration/Setup',

            // General
            'fas fa-box' => 'General Product',
            'fas fa-star' => 'Premium Service',
            'fas fa-rocket' => 'Growth/Optimization',
            'fas fa-puzzle-piece' => 'Integration Services',
        ];
    }

    /**
     * Get the user who created this product
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the businesses that have this product
     */
    public function businesses()
    {
        return $this->belongsToMany(Business::class, 'business_products')
                    ->withPivot('assigned_by')
                    ->withTimestamps();
    }

    /**
     * Get icon label
     */
    public function getIconLabelAttribute(): string
    {
        return static::getAvailableIcons()[$this->icon] ?? 'General Product';
    }

    /**
     * Scope to get active products
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to search products
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where('name', 'like', "%{$search}%");
    }

    /**
     * Scope to get products with business count
     */
    public function scopeWithBusinessCount($query)
    {
        return $query->withCount('businesses');
    }

    /**
     * Scope to get popular products
     */
    public function scopePopular($query, int $limit = 10)
    {
        return $query->withCount('businesses')
                    ->orderBy('businesses_count', 'desc')
                    ->limit($limit);
    }

    /**
     * Ensure unique slug
     */
    public function ensureUniqueSlug(): void
    {
        $originalSlug = $this->slug;
        $counter = 1;

        while (static::where('slug', $this->slug)->where('id', '!=', $this->id)->exists()) {
            $this->slug = $originalSlug . '-' . $counter;
            $counter++;
        }
    }

    /**
     * Get usage statistics
     */
    public function getUsageStats(): array
    {
        return [
            'total_businesses' => $this->businesses()->count(),
            'active_assignments' => $this->businesses()->count(),
        ];
    }
}
