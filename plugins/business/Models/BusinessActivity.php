<?php

namespace Plugins\Business\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use App\Models\User;

class BusinessActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'user_id',
        'type',
        'category',
        'severity',
        'title',
        'description',
        'metadata',
        'subject_type',
        'subject_id',
        'is_visible',
        'is_system_generated',
        'edited_at',
        'reply_to_id',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_visible' => 'boolean',
        'is_system_generated' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'edited_at' => 'datetime',
    ];

    /**
     * Append additional attributes to JSON
     */
    protected $appends = ['type_info', 'severity_info'];

    /**
     * Activity types with their display information
     */
    public static function getActivityTypes(): array
    {
        return [
            'comment' => [
                'label' => 'Comment',
                'icon' => 'fas fa-comment',
                'color' => 'blue'
            ],
            'communication' => [
                'label' => 'Communication',
                'icon' => 'fas fa-comments',
                'color' => 'green'
            ],
            'chat' => [
                'label' => 'Chat Message',
                'icon' => 'fas fa-comment-dots',
                'color' => 'blue'
            ],
            'email' => [
                'label' => 'Email',
                'icon' => 'fas fa-envelope',
                'color' => 'red'
            ],
            'phone' => [
                'label' => 'Phone Call',
                'icon' => 'fas fa-phone',
                'color' => 'green'
            ],
            'meeting' => [
                'label' => 'Meeting',
                'icon' => 'fas fa-handshake',
                'color' => 'purple'
            ],
            'visit' => [
                'label' => 'Site Visit',
                'icon' => 'fas fa-building',
                'color' => 'orange'
            ],
            'whatsapp' => [
                'label' => 'WhatsApp',
                'icon' => 'fab fa-whatsapp',
                'color' => 'green'
            ],
            'sms' => [
                'label' => 'SMS',
                'icon' => 'fas fa-sms',
                'color' => 'blue'
            ],
            'reminder' => [
                'label' => 'Reminder',
                'icon' => 'fas fa-bell',
                'color' => 'yellow'
            ],
            'follow_up' => [
                'label' => 'Follow-up',
                'icon' => 'fas fa-redo',
                'color' => 'indigo'
            ],
            'document_upload' => [
                'label' => 'Document Upload',
                'icon' => 'fas fa-upload',
                'color' => 'green'
            ],
            'document_delete' => [
                'label' => 'Document Deleted',
                'icon' => 'fas fa-trash',
                'color' => 'red'
            ],
            'status_change' => [
                'label' => 'Status Change',
                'icon' => 'fas fa-exchange-alt',
                'color' => 'orange'
            ],
            'tag_assignment' => [
                'label' => 'Tag Added',
                'icon' => 'fas fa-tag',
                'color' => 'purple'
            ],
            'tag_removal' => [
                'label' => 'Tag Removed',
                'icon' => 'fas fa-tag',
                'color' => 'gray'
            ],
            'product_assignment' => [
                'label' => 'Product Added',
                'icon' => 'fas fa-box',
                'color' => 'indigo'
            ],
            'product_removal' => [
                'label' => 'Product Removed',
                'icon' => 'fas fa-box',
                'color' => 'gray'
            ],
            'user_assignment' => [
                'label' => 'User Assigned',
                'icon' => 'fas fa-user-plus',
                'color' => 'green'
            ],
            'user_removal' => [
                'label' => 'User Removed',
                'icon' => 'fas fa-user-minus',
                'color' => 'red'
            ],
            'business_update' => [
                'label' => 'Business Updated',
                'icon' => 'fas fa-edit',
                'color' => 'blue'
            ],
            'system_log' => [
                'label' => 'System Log',
                'icon' => 'fas fa-cog',
                'color' => 'gray'
            ],
            'manual_log' => [
                'label' => 'Manual Log',
                'icon' => 'fas fa-clipboard',
                'color' => 'yellow'
            ],
            'file_share' => [
                'label' => 'File Shared',
                'icon' => 'fas fa-share',
                'color' => 'teal'
            ],
            'mention' => [
                'label' => 'Mention',
                'icon' => 'fas fa-at',
                'color' => 'pink'
            ],
        ];
    }

    /**
     * Severity levels with their display information
     */
    public static function getSeverityLevels(): array
    {
        return [
            'info' => [
                'label' => 'Info',
                'color' => 'blue',
                'badge' => 'bg-blue-100 text-blue-800'
            ],
            'warning' => [
                'label' => 'Warning',
                'color' => 'yellow',
                'badge' => 'bg-yellow-100 text-yellow-800'
            ],
            'important' => [
                'label' => 'Important',
                'color' => 'orange',
                'badge' => 'bg-orange-100 text-orange-800'
            ],
            'critical' => [
                'label' => 'Critical',
                'color' => 'red',
                'badge' => 'bg-red-100 text-red-800'
            ],
        ];
    }

    /**
     * Get the business this activity belongs to
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the user who performed this activity
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subject of this activity (polymorphic)
     */
    public function subject(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get comments for this activity
     */
    public function comments(): HasMany
    {
        return $this->hasMany(BusinessComment::class, 'activity_id');
    }

    /**
     * Get the activity this is a reply to
     */
    public function replyTo(): BelongsTo
    {
        return $this->belongsTo(BusinessActivity::class, 'reply_to_id');
    }

    /**
     * Get replies to this activity
     */
    public function replies(): HasMany
    {
        return $this->hasMany(BusinessActivity::class, 'reply_to_id');
    }

    /**
     * Get attachments for this activity
     */
    public function attachments(): HasMany
    {
        return $this->hasMany(BusinessActivityAttachment::class, 'activity_id');
    }

    /**
     * Get activity type information
     */
    public function getTypeInfoAttribute(): array
    {
        return static::getActivityTypes()[$this->type] ?? [
            'label' => ucfirst($this->type),
            'icon' => 'fas fa-circle',
            'color' => 'gray'
        ];
    }

    /**
     * Get severity information
     */
    public function getSeverityInfoAttribute(): array
    {
        return static::getSeverityLevels()[$this->severity] ?? [
            'label' => ucfirst($this->severity),
            'color' => 'gray',
            'badge' => 'bg-gray-100 text-gray-800'
        ];
    }

    /**
     * Scope for visible activities
     */
    public function scopeVisible($query)
    {
        return $query->where('is_visible', true);
    }

    /**
     * Scope for activities by type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for activities by category
     */
    public function scopeOfCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for activities by severity
     */
    public function scopeOfSeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Create a new activity
     */
    public static function createActivity(array $data): self
    {
        return static::create($data);
    }

    /**
     * Log a system activity
     */
    public static function logSystem(Business $business, string $title, string $description = null, array $metadata = []): self
    {
        return static::createActivity([
            'business_id' => $business->id,
            'user_id' => auth()->id(),
            'type' => 'system_log',
            'category' => 'system',
            'severity' => 'info',
            'title' => $title,
            'description' => $description,
            'metadata' => $metadata,
            'is_system_generated' => true,
        ]);
    }

    /**
     * Log a user action
     */
    public static function logUserAction(Business $business, string $type, string $title, string $description = null, array $metadata = []): self
    {
        return static::createActivity([
            'business_id' => $business->id,
            'user_id' => auth()->id(),
            'type' => $type,
            'category' => 'user_action',
            'severity' => 'info',
            'title' => $title,
            'description' => $description,
            'metadata' => $metadata,
            'is_system_generated' => false,
        ]);
    }
}
