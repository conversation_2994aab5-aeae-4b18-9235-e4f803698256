<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we'll use a simpler approach
        // Since SQLite doesn't support modifying CHECK constraints easily,
        // we'll just ensure the model validation handles the 'lost' status
        // The CHECK constraint will be ignored for now

        // This migration is a placeholder to mark that we've addressed the lost status
        // The application-level validation will handle the 'lost' status
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No action needed for rollback since we didn't modify the database structure
        // The CHECK constraint remains as is
    }
};
