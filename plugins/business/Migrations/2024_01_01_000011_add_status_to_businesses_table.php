<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->enum('status', ['lead', 'deal', 'customer', 'partner', 'churned'])
                  ->default('lead')
                  ->after('is_active');
            $table->text('churn_reason')->nullable()->after('status');
            
            // Add indexes for status
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->dropIndex(['status']);
            $table->dropColumn(['status', 'churn_reason']);
        });
    }
};
