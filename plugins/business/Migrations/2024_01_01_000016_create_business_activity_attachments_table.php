<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_activity_attachments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('activity_id')->constrained('business_activities')->onDelete('cascade');
            $table->foreignId('business_id')->constrained('businesses')->onDelete('cascade');
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            
            // File information
            $table->string('filename');
            $table->string('original_filename');
            $table->string('file_path');
            $table->string('file_type'); // MIME type
            $table->string('file_extension');
            $table->unsignedBigInteger('file_size'); // in bytes
            
            // File categorization
            $table->enum('attachment_type', [
                'document',
                'image',
                'video',
                'audio',
                'archive',
                'other'
            ])->default('document');
            
            // Thumbnail/preview
            $table->string('thumbnail_path')->nullable();
            $table->boolean('has_preview')->default(false);
            
            // File metadata
            $table->json('metadata')->nullable(); // Store additional file info
            
            // Access control
            $table->boolean('is_public')->default(false);
            $table->json('allowed_users')->nullable(); // Array of user IDs who can access
            
            // Download tracking
            $table->unsignedInteger('download_count')->default(0);
            $table->timestamp('last_downloaded_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['activity_id']);
            $table->index(['business_id', 'created_at']);
            $table->index(['uploaded_by']);
            $table->index(['attachment_type']);
            $table->index(['file_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_activity_attachments');
    }
};
