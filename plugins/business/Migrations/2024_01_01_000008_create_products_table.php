<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->enum('category', [
                'communication',
                'automation',
                'marketing',
                'analytics',
                'support',
                'other'
            ])->default('other');
            $table->decimal('base_price', 10, 2)->nullable();
            $table->string('pricing_model')->nullable(); // monthly, yearly, one-time, custom
            $table->boolean('is_active')->default(true);
            $table->json('features')->nullable(); // Store product features as JSON
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            // Indexes for performance
            $table->index(['name']);
            $table->index(['slug']);
            $table->index(['category']);
            $table->index(['is_active']);
            $table->index(['created_by']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
