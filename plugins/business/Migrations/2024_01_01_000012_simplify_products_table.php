<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Check if columns exist before dropping them
            if (Schema::hasColumn('products', 'description')) {
                $table->dropColumn('description');
            }
            if (Schema::hasColumn('products', 'category')) {
                $table->dropColumn('category');
            }
            if (Schema::hasColumn('products', 'base_price')) {
                $table->dropColumn('base_price');
            }
            if (Schema::hasColumn('products', 'pricing_model')) {
                $table->dropColumn('pricing_model');
            }
            if (Schema::hasColumn('products', 'features')) {
                $table->dropColumn('features');
            }

            // Add simple icon field if it doesn't exist
            if (!Schema::hasColumn('products', 'icon')) {
                $table->string('icon')->default('fas fa-box')->after('slug');
            }
        });

        // Simplify business_products pivot table
        Schema::table('business_products', function (Blueprint $table) {
            // Remove complex pivot fields if they exist
            if (Schema::hasColumn('business_products', 'custom_price')) {
                $table->dropColumn('custom_price');
            }
            if (Schema::hasColumn('business_products', 'pricing_model')) {
                $table->dropColumn('pricing_model');
            }
            if (Schema::hasColumn('business_products', 'status')) {
                $table->dropColumn('status');
            }
            if (Schema::hasColumn('business_products', 'start_date')) {
                $table->dropColumn('start_date');
            }
            if (Schema::hasColumn('business_products', 'end_date')) {
                $table->dropColumn('end_date');
            }
            if (Schema::hasColumn('business_products', 'notes')) {
                $table->dropColumn('notes');
            }
            if (Schema::hasColumn('business_products', 'custom_features')) {
                $table->dropColumn('custom_features');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Remove icon field
            $table->dropColumn('icon');

            // Restore complex fields
            $table->text('description')->nullable()->after('slug');
            $table->enum('category', [
                'communication',
                'automation',
                'marketing',
                'analytics',
                'support',
                'other'
            ])->default('other')->after('description');
            $table->decimal('base_price', 10, 2)->nullable()->after('category');
            $table->string('pricing_model')->nullable()->after('base_price');
            $table->json('features')->nullable()->after('pricing_model');

            // Restore index
            $table->index(['category']);
        });

        Schema::table('business_products', function (Blueprint $table) {
            // Restore complex pivot fields
            $table->decimal('custom_price', 10, 2)->nullable()->after('product_id');
            $table->string('pricing_model')->nullable()->after('custom_price');
            $table->enum('status', ['active', 'inactive', 'pending', 'cancelled'])->default('active')->after('pricing_model');
            $table->date('start_date')->nullable()->after('status');
            $table->date('end_date')->nullable()->after('start_date');
            $table->text('notes')->nullable()->after('end_date');
            $table->json('custom_features')->nullable()->after('notes');
        });
    }
};
