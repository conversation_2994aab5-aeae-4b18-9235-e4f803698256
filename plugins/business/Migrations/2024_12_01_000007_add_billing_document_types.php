<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For SQLite, we can't modify ENUM constraints easily
        // The application-level validation will handle the new document types
        // This migration is a placeholder to mark that we've added the new types
        
        // The new document types (invoice, quotation, payment, receipt) will be
        // validated at the application level in the Document model
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No action needed for rollback since we didn't modify the database structure
    }
};
