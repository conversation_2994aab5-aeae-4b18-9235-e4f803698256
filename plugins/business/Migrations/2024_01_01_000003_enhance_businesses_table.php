<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->string('brand_name')->nullable()->after('name');
            $table->string('legal_name')->nullable()->after('brand_name');
            $table->string('website_url')->nullable()->after('website');
            $table->string('primary_phone')->nullable()->after('phone');
            
            // Add indexes for new fields
            $table->index(['brand_name']);
            $table->index(['legal_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->dropIndex(['brand_name']);
            $table->dropIndex(['legal_name']);
            $table->dropColumn(['brand_name', 'legal_name', 'website_url', 'primary_phone']);
        });
    }
};
