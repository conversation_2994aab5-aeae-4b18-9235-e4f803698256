<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if columns already exist before adding them
        if (!Schema::hasColumn('businesses', 'lost_reason')) {
            Schema::table('businesses', function (Blueprint $table) {
                $table->text('lost_reason')->nullable()->after('churn_reason');
            });
        }

        if (!Schema::hasColumn('businesses', 'lost_at')) {
            Schema::table('businesses', function (Blueprint $table) {
                $table->timestamp('lost_at')->nullable()->after('churned_at');
            });
        }

        // SQLite doesn't support ENUM, so we'll skip the ALTER statement
        // The validation in the model and controller will handle the 'lost' status
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            if (Schema::hasColumn('businesses', 'lost_reason')) {
                $table->dropColumn('lost_reason');
            }
            if (Schema::hasColumn('businesses', 'lost_at')) {
                $table->dropColumn('lost_at');
            }
        });

        // SQLite doesn't support ENUM modifications
    }
};
