<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Define admin plugin permissions
        $permissions = [
            [
                'name' => 'view_admin',
                'display_name' => 'View Admin',
                'description' => 'Can view admin dashboard and basic information',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_admin',
                'display_name' => 'Manage Admin',
                'description' => 'Can manage admin settings and configurations',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_crm',
                'display_name' => 'View CRM',
                'description' => 'Can view CRM leads and prospects',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_crm',
                'display_name' => 'Manage CRM',
                'description' => 'Can create, edit, and manage CRM leads',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_clients',
                'display_name' => 'View Clients',
                'description' => 'Can view client information and data',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_clients',
                'display_name' => 'Manage Clients',
                'description' => 'Can create, edit, and manage clients',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_invoices',
                'display_name' => 'View Invoices',
                'description' => 'Can view invoices and billing information',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_invoices',
                'display_name' => 'Manage Invoices',
                'description' => 'Can create, edit, and manage invoices',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'view_business_info',
                'display_name' => 'View Business Info',
                'description' => 'Can view business information and settings',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'manage_business_info',
                'display_name' => 'Manage Business Info',
                'description' => 'Can edit and manage business information',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // Insert permissions (ignore duplicates)
        foreach ($permissions as $permission) {
            $exists = DB::table('permissions')->where('name', $permission['name'])->exists();
            if (!$exists) {
                DB::table('permissions')->insert($permission);
            }
        }

        // Assign permissions to admin role
        $adminRole = DB::table('roles')->where('name', 'admin')->first();
        if ($adminRole) {
            $permissionNames = array_column($permissions, 'name');
            $createdPermissions = DB::table('permissions')->whereIn('name', $permissionNames)->get();
            
            foreach ($createdPermissions as $permission) {
                // Check if permission is already assigned
                $exists = DB::table('permission_role')
                    ->where('role_id', $adminRole->id)
                    ->where('permission_id', $permission->id)
                    ->exists();

                if (!$exists) {
                    DB::table('permission_role')->insert([
                        'role_id' => $adminRole->id,
                        'permission_id' => $permission->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove admin plugin permissions
        $permissionNames = [
            'view_admin',
            'manage_admin',
            'view_crm',
            'manage_crm',
            'view_clients',
            'manage_clients',
            'view_invoices',
            'manage_invoices',
            'view_business_info',
            'manage_business_info',
        ];

        // Get permission IDs
        $permissionIds = DB::table('permissions')->whereIn('name', $permissionNames)->pluck('id');

        // Remove role permissions
        DB::table('permission_role')->whereIn('permission_id', $permissionIds)->delete();

        // Remove permissions
        DB::table('permissions')->whereIn('name', $permissionNames)->delete();
    }
};
