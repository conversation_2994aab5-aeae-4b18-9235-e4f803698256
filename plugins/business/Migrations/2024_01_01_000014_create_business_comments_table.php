<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_comments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained('businesses')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('activity_id')->nullable()->constrained('business_activities')->onDelete('cascade');
            
            // Comment content
            $table->text('content');
            $table->text('content_html')->nullable(); // Rendered HTML version
            
            // Comment metadata
            $table->json('mentions')->nullable(); // Array of mentioned user IDs
            $table->boolean('is_edited')->default(false);
            $table->timestamp('edited_at')->nullable();
            
            // Reply system (optional for future enhancement)
            $table->foreignId('parent_id')->nullable()->constrained('business_comments')->onDelete('cascade');
            
            // Visibility
            $table->boolean('is_visible')->default(true);
            
            $table->timestamps();
            
            // Indexes
            $table->index(['business_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['activity_id']);
            $table->index(['parent_id']);
            $table->index(['is_visible', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_comments');
    }
};
