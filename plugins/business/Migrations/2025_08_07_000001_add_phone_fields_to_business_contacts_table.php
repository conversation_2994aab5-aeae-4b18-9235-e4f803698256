<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('business_contacts', function (Blueprint $table) {
            // Add phone extension for primary phone
            $table->string('phone_ext')->nullable()->after('phone');
            
            // Add second phone number and its extension
            $table->string('phone2')->nullable()->after('phone_ext');
            $table->string('phone2_ext')->nullable()->after('phone2');
            
            // Add indexes for the new phone fields
            $table->index(['phone2']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('business_contacts', function (Blueprint $table) {
            $table->dropIndex(['phone2']);
            $table->dropColumn(['phone_ext', 'phone2', 'phone2_ext']);
        });
    }
};
