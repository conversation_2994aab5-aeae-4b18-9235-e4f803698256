<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained('businesses')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            
            // Activity type and categorization
            $table->enum('type', [
                'comment',
                'document_upload',
                'document_delete',
                'status_change',
                'tag_assignment',
                'tag_removal',
                'product_assignment',
                'product_removal',
                'user_assignment',
                'user_removal',
                'business_update',
                'system_log',
                'manual_log',
                'file_share',
                'mention'
            ]);
            
            $table->enum('category', [
                'system',
                'user_action',
                'business_event',
                'communication',
                'document',
                'assignment'
            ])->default('user_action');
            
            $table->enum('severity', [
                'info',
                'warning',
                'important',
                'critical'
            ])->default('info');
            
            // Activity content
            $table->string('title');
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // Store additional data like old/new values
            
            // Related entities (polymorphic relationships)
            $table->string('subject_type')->nullable(); // Model class name
            $table->unsignedBigInteger('subject_id')->nullable(); // Model ID
            
            // Visibility and status
            $table->boolean('is_visible')->default(true);
            $table->boolean('is_system_generated')->default(false);
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['business_id', 'created_at']);
            $table->index(['type', 'category']);
            $table->index(['user_id', 'created_at']);
            $table->index(['subject_type', 'subject_id']);
            $table->index(['is_visible', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_activities');
    }
};
