<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('business_activities', function (Blueprint $table) {
            $table->timestamp('edited_at')->nullable()->after('updated_at');
            $table->unsignedBigInteger('reply_to_id')->nullable()->after('business_id');
            
            $table->foreign('reply_to_id')->references('id')->on('business_activities')->onDelete('set null');
            $table->index('reply_to_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('business_activities', function (Blueprint $table) {
            $table->dropForeign(['reply_to_id']);
            $table->dropIndex(['reply_to_id']);
            $table->dropColumn(['edited_at', 'reply_to_id']);
        });
    }
};
