<?php

namespace Plugins\Business\Seeds;

use Illuminate\Database\Seeder;
use Plugins\Business\Models\Product;
use App\Models\User;

class ProductsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user to assign as creator
        $adminUser = User::whereHas('role', function ($query) {
            $query->where('name', 'admin');
        })->first();

        if (!$adminUser) {
            $adminUser = User::first();
        }

        if (!$adminUser) {
            return; // No users exist
        }

        $products = [
            [
                'name' => 'WhatsApp Business',
                'slug' => 'whatsapp-business',
                'description' => 'Professional WhatsApp Business API integration for customer communication',
                'category' => 'communication',
                'base_price' => 99.00,
                'pricing_model' => 'monthly',
                'features' => json_encode([
                    'WhatsApp Business API',
                    'Message Templates',
                    'Bulk Messaging',
                    'Media Support',
                    'Delivery Reports'
                ])
            ],
            [
                'name' => 'Chatbot',
                'slug' => 'chatbot',
                'description' => 'Automated chatbot for customer support and engagement',
                'category' => 'automation',
                'base_price' => 149.00,
                'pricing_model' => 'monthly',
                'features' => json_encode([
                    'Rule-based Responses',
                    'Multi-platform Support',
                    'Analytics Dashboard',
                    'Custom Workflows',
                    '24/7 Availability'
                ])
            ],
            [
                'name' => 'Live Chat',
                'slug' => 'live-chat',
                'description' => 'Real-time live chat support for websites and applications',
                'category' => 'support',
                'base_price' => 79.00,
                'pricing_model' => 'monthly',
                'features' => json_encode([
                    'Real-time Messaging',
                    'Agent Dashboard',
                    'Chat History',
                    'File Sharing',
                    'Visitor Tracking'
                ])
            ],
            [
                'name' => 'AI Chatbot',
                'slug' => 'ai-chatbot',
                'description' => 'Advanced AI-powered chatbot with natural language processing',
                'category' => 'automation',
                'base_price' => 299.00,
                'pricing_model' => 'monthly',
                'features' => json_encode([
                    'Natural Language Processing',
                    'Machine Learning',
                    'Context Understanding',
                    'Multi-language Support',
                    'Advanced Analytics'
                ])
            ],
            [
                'name' => 'Call Bot',
                'slug' => 'call-bot',
                'description' => 'Automated voice response system for phone calls',
                'category' => 'automation',
                'base_price' => 199.00,
                'pricing_model' => 'monthly',
                'features' => json_encode([
                    'Voice Recognition',
                    'Text-to-Speech',
                    'Call Routing',
                    'IVR System',
                    'Call Analytics'
                ])
            ],
            [
                'name' => 'Voice Services',
                'slug' => 'voice-services',
                'description' => 'Comprehensive voice communication solutions',
                'category' => 'communication',
                'base_price' => 249.00,
                'pricing_model' => 'monthly',
                'features' => json_encode([
                    'Voice Calls',
                    'Conference Calling',
                    'Call Recording',
                    'Voicemail',
                    'Number Porting'
                ])
            ],
            [
                'name' => 'Email Marketing',
                'slug' => 'email-marketing',
                'description' => 'Professional email marketing and automation platform',
                'category' => 'marketing',
                'base_price' => 129.00,
                'pricing_model' => 'monthly',
                'features' => json_encode([
                    'Email Campaigns',
                    'Automation Workflows',
                    'A/B Testing',
                    'Analytics & Reports',
                    'Template Library'
                ])
            ]
        ];

        foreach ($products as $productData) {
            Product::firstOrCreate(
                ['slug' => $productData['slug']],
                array_merge($productData, ['created_by' => $adminUser->id])
            );
        }
    }
}
