<?php

namespace Plugins\Business\Seeds;

use Illuminate\Database\Seeder;
use Plugins\Business\Models\Business;
use Plugins\Business\Models\Tag;
use Plugins\Products\Models\Product;
use App\Models\User;

class BusinessSampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user or create a default user
        $user = User::whereHas('role', function ($query) {
            $query->where('name', 'admin');
        })->first();

        if (!$user) {
            $user = User::first();
        }

        if (!$user) {
            $this->command->warn("No users found. Please create users first.");
            return;
        }

        // Create sample tags
        $tags = [
            ['name' => 'E-commerce', 'color' => '#3B82F6'],
            ['name' => 'SaaS', 'color' => '#10B981'],
            ['name' => 'Enterprise', 'color' => '#8B5CF6'],
            ['name' => 'Startup', 'color' => '#F59E0B'],
            ['name' => 'Healthcare', 'color' => '#EF4444'],
            ['name' => 'Education', 'color' => '#06B6D4'],
        ];

        foreach ($tags as $tagData) {
            Tag::firstOrCreate(['name' => $tagData['name']], array_merge($tagData, [
                'created_by' => $user->id,
                'is_active' => true,
            ]));
        }

        $this->command->info("Created sample tags");

        // Create sample businesses
        $businesses = [
            [
                'name' => 'TechCorp Solutions',
                'description' => 'Leading technology solutions provider specializing in enterprise software development and digital transformation.',
                'email' => '<EMAIL>',
                'phone' => '+966-11-123-4567',
                'website' => 'https://techcorp.com',
                'address' => 'King Fahd Road, Riyadh, Saudi Arabia',
                'status' => 'customer',
                'tags' => ['Enterprise', 'SaaS'],
            ],
            [
                'name' => 'Digital Marketing Hub',
                'description' => 'Full-service digital marketing agency helping businesses grow their online presence through innovative strategies.',
                'email' => '<EMAIL>',
                'phone' => '+966-12-987-6543',
                'website' => 'https://digitalmarketing.com',
                'address' => 'Corniche Road, Jeddah, Saudi Arabia',
                'status' => 'lead',
                'tags' => ['Startup', 'E-commerce'],
            ],
            [
                'name' => 'HealthTech Innovations',
                'description' => 'Revolutionary healthcare technology company developing AI-powered diagnostic tools and patient management systems.',
                'email' => '<EMAIL>',
                'phone' => '+966-13-555-0123',
                'website' => 'https://healthtech.com',
                'address' => 'KFUPM Campus, Dhahran, Saudi Arabia',
                'status' => 'partner',
                'tags' => ['Healthcare', 'Enterprise'],
            ],
            [
                'name' => 'EduLearn Platform',
                'description' => 'Online education platform providing interactive learning experiences for students and professionals.',
                'email' => '<EMAIL>',
                'phone' => '+966-11-777-8888',
                'website' => 'https://edulearn.com',
                'address' => 'Olaya District, Riyadh, Saudi Arabia',
                'status' => 'customer',
                'tags' => ['Education', 'SaaS'],
            ],
            [
                'name' => 'RetailMax Systems',
                'description' => 'Comprehensive retail management solutions including POS systems, inventory management, and customer analytics.',
                'email' => '<EMAIL>',
                'phone' => '+966-12-444-5555',
                'website' => 'https://retailmax.com',
                'address' => 'Al Hamra District, Jeddah, Saudi Arabia',
                'status' => 'deal',
                'tags' => ['E-commerce', 'Enterprise'],
            ],
        ];

        foreach ($businesses as $businessData) {
            $tagNames = $businessData['tags'];
            unset($businessData['tags']);

            // Create business
            $business = Business::create([
                'name' => $businessData['name'],
                'description' => $businessData['description'],
                'email' => $businessData['email'],
                'phone' => $businessData['phone'],
                'website' => $businessData['website'],
                'address' => $businessData['address'],
                'status' => $businessData['status'],
                'is_active' => true,
                'created_by' => $user->id,
            ]);

            $this->command->info("Created business: {$business->name}");

            // Attach tags
            $tags = Tag::whereIn('name', $tagNames)->get();
            foreach ($tags as $tag) {
                $business->tags()->attach($tag->id, [
                    'assigned_by' => $user->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            // Assign user to business
            $business->users()->attach($user->id, [
                'role' => 'admin',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Attach random products if they exist
            $products = Product::active()->inRandomOrder()->limit(rand(1, 3))->get();
            foreach ($products as $product) {
                $business->products()->attach($product->id, [
                    'status' => 'active',
                    'start_date' => now(),
                    'assigned_by' => $user->id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        $this->command->info("Sample businesses seeded successfully!");
    }
}
