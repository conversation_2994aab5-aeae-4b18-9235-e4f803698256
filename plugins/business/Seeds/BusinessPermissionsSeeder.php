<?php

namespace Plugins\Business\Seeds;

use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class BusinessPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create business-related permissions
        $permissions = [
            [
                'name' => 'manage_businesses',
                'display_name' => 'Manage Businesses',
                'description' => 'Create, edit, and delete business entities',
                'plugin' => 'business'
            ],
            [
                'name' => 'view_businesses',
                'display_name' => 'View Businesses',
                'description' => 'Read-only access to business data',
                'plugin' => 'business'
            ],
            [
                'name' => 'manage_business_contacts',
                'display_name' => 'Manage Business Contacts',
                'description' => 'Create, edit, and delete business contacts',
                'plugin' => 'business'
            ],
            [
                'name' => 'view_business_contacts',
                'display_name' => 'View Business Contacts',
                'description' => 'Read-only access to business contacts',
                'plugin' => 'business'
            ],
            [
                'name' => 'manage_business_documents',
                'display_name' => 'Manage Business Documents',
                'description' => 'Upload, edit, and delete business documents',
                'plugin' => 'business'
            ],
            [
                'name' => 'view_business_documents',
                'display_name' => 'View Business Documents',
                'description' => 'Read-only access to business documents',
                'plugin' => 'business'
            ],
            [
                'name' => 'manage_business_activities',
                'display_name' => 'Manage Business Activities',
                'description' => 'Create, edit, and delete business activities',
                'plugin' => 'business'
            ],
            [
                'name' => 'view_business_activities',
                'display_name' => 'View Business Activities',
                'description' => 'Read-only access to business activities',
                'plugin' => 'business'
            ],
            [
                'name' => 'import_businesses',
                'display_name' => 'Import Businesses',
                'description' => 'Import business data from external files',
                'plugin' => 'business'
            ],
            [
                'name' => 'export_businesses',
                'display_name' => 'Export Businesses',
                'description' => 'Export business data to external files',
                'plugin' => 'business'
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission['name']], $permission);
        }

        // Assign all business permissions to admin role
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $businessPermissions = Permission::whereIn('name', [
                'manage_businesses',
                'view_businesses',
                'manage_business_contacts',
                'view_business_contacts',
                'manage_business_documents',
                'view_business_documents',
                'manage_business_activities',
                'view_business_activities',
                'import_businesses',
                'export_businesses'
            ])->get();

            foreach ($businessPermissions as $permission) {
                if (!$adminRole->permissions->contains($permission)) {
                    $adminRole->permissions()->attach($permission);
                }
            }
        }

        // Assign view and import permissions to editor role
        $editorRole = Role::where('name', 'editor')->first();
        if ($editorRole) {
            $editorPermissions = Permission::whereIn('name', [
                'view_businesses',
                'view_business_contacts',
                'view_business_documents',
                'view_business_activities',
                'import_businesses'
            ])->get();

            foreach ($editorPermissions as $permission) {
                if (!$editorRole->permissions->contains($permission)) {
                    $editorRole->permissions()->attach($permission);
                }
            }
        }

        // Assign basic permissions to user role
        $userRole = Role::where('name', 'user')->first();
        if ($userRole) {
            $userPermissions = Permission::whereIn('name', [
                'view_businesses',
                'view_business_contacts',
                'import_businesses'
            ])->get();

            foreach ($userPermissions as $permission) {
                if (!$userRole->permissions->contains($permission)) {
                    $userRole->permissions()->attach($permission);
                }
            }
        }
    }
}
