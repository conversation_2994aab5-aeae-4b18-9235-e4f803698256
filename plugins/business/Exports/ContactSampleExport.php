<?php

namespace Plugins\Business\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ContactSampleExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    /**
     * Return sample data array
     */
    public function array(): array
    {
        return [
            [
                '<PERSON>',
                'أحمد الراشد',
                'Taqnyat',
                '',
                'CEO',
                'Executive',
                '<EMAIL>',
                '+966-11-123-4567',
                '101',
                '+966-50-123-4567',
                '102',
                true,
                'Primary contact for all executive decisions'
            ],
            [
                'Fat<PERSON>',
                'فاطمة الزهراء',
                'Taqnyat',
                '',
                'CTO',
                'Technology',
                '<EMAIL>',
                '+966-11-123-4568',
                '',
                '+966-50-123-4568',
                '201',
                false,
                'Technical lead for all IT projects'
            ],
            [
                'Omar Al-Mansouri',
                'عمر المنصوري',
                'Taqnyat',
                '',
                'Project Manager',
                'Operations',
                '<EMAIL>',
                '+966-11-123-4569',
                '300',
                '',
                '',
                false,
                'Main point of contact for project coordination'
            ],
            [
                'Layla Al-Harbi',
                'ليلى الحربي',
                'Taqnyat',
                '',
                'Sales Director',
                'Sales',
                '<EMAIL>',
                '+966-11-123-4570',
                '',
                '+966-50-123-4570',
                '',
                false,
                'Handles all sales inquiries and partnerships'
            ]
        ];
    }

    /**
     * Return the headings for the Excel file
     */
    public function headings(): array
    {
        return [
            'name',
            'arabic_name',
            'business_name',
            'business_email',
            'position',
            'department',
            'email',
            'phone',
            'phone_ext',
            'phone2',
            'phone2_ext',
            'is_primary',
            'notes'
        ];
    }

    /**
     * Apply styles to the worksheet
     */
    public function styles(Worksheet $sheet)
    {
        // Style the header row
        $sheet->getStyle('A1:M1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '059669'], // Green color
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Add borders to all cells with data
        $sheet->getStyle('A1:M5')->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => 'CCCCCC'],
                ],
            ],
        ]);

        // Center align all data
        $sheet->getStyle('A2:M5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('A2:M5')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

        return $sheet;
    }

    /**
     * Set column widths
     */
    public function columnWidths(): array
    {
        return [
            'A' => 20, // name
            'B' => 25, // arabic_name
            'C' => 20, // business_name
            'D' => 25, // business_email
            'E' => 20, // position
            'F' => 15, // department
            'G' => 25, // email
            'H' => 15, // phone
            'I' => 8,  // phone_ext
            'J' => 15, // phone2
            'K' => 8,  // phone2_ext
            'L' => 12, // is_primary
            'M' => 30, // notes
        ];
    }
}
