<?php

namespace Plugins\Business\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class BusinessSampleExport implements FromArray, WithHeadings, WithStyles, WithColumnWidths
{
    /**
     * Return sample data array
     */
    public function array(): array
    {
        return [
            [
                'Acme Corporation',
                'Acme Corp',
                'Acme Corporation LLC',
                'Leading provider of innovative solutions',
                '<EMAIL>',
                '+1-555-0123',
                '+1-555-0123',
                '123 Business Street',
                'New York',
                'NY',
                'USA',
                '10001',
                'https://www.acme.com',
                'https://www.acme.com',
                'TAX123456789',
                'customer'
            ],
            [
                'Tech Solutions Inc',
                'TechSol',
                'Tech Solutions Incorporated',
                'Software development and consulting services',
                '<EMAIL>',
                '+1-555-0456',
                '+1-555-0456',
                '456 Tech Avenue',
                'San Francisco',
                'CA',
                'USA',
                '94105',
                'https://www.techsolutions.com',
                'https://www.techsolutions.com',
                'TAX987654321',
                'lead'
            ],
            [
                'Global Enterprises',
                'GlobalEnt',
                'Global Enterprises Ltd',
                'International business solutions',
                '<EMAIL>',
                '+44-20-1234-5678',
                '+44-20-1234-5678',
                '789 International Blvd',
                'London',
                'England',
                'UK',
                'SW1A 1AA',
                'https://www.globalent.com',
                'https://www.globalent.com',
                'GB123456789',
                'partner'
            ]
        ];
    }

    /**
     * Return the headings for the Excel file
     */
    public function headings(): array
    {
        return [
            'name',
            'brand_name',
            'legal_name',
            'description',
            'email',
            'phone',
            'primary_phone',
            'address',
            'city',
            'state',
            'country',
            'postal_code',
            'website',
            'website_url',
            'tax_id',
            'status'
        ];
    }

    /**
     * Apply styles to the worksheet
     */
    public function styles(Worksheet $sheet)
    {
        // Style the header row
        $sheet->getStyle('A1:P1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4F46E5'], // Indigo color
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ]);

        // Add borders to all cells with data
        $sheet->getStyle('A1:P4')->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => 'CCCCCC'],
                ],
            ],
        ]);

        // Center align all data
        $sheet->getStyle('A2:P4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle('A2:P4')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

        return $sheet;
    }

    /**
     * Set column widths
     */
    public function columnWidths(): array
    {
        return [
            'A' => 20, // name
            'B' => 15, // brand_name
            'C' => 20, // legal_name
            'D' => 30, // description
            'E' => 25, // email
            'F' => 15, // phone
            'G' => 15, // primary_phone
            'H' => 25, // address
            'I' => 15, // city
            'J' => 10, // state
            'K' => 12, // country
            'L' => 12, // postal_code
            'M' => 25, // website
            'N' => 25, // website_url
            'O' => 15, // tax_id
            'P' => 12, // status
        ];
    }
}
