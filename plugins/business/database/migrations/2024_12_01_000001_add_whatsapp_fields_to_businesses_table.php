<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            // Check if columns don't already exist before adding them
            if (!Schema::hasColumn('businesses', 'whatsapp_enabled')) {
                $table->boolean('whatsapp_enabled')->default(false)->after('is_active');
            }
            if (!Schema::hasColumn('businesses', 'meta_business_id')) {
                $table->string('meta_business_id')->nullable()->after('whatsapp_enabled');
            }
            if (!Schema::hasColumn('businesses', 'whatsapp_id')) {
                $table->string('whatsapp_id')->nullable()->after('meta_business_id');
            }
            if (!Schema::hasColumn('businesses', 'whatsapp_provider')) {
                $table->enum('whatsapp_provider', ['taqnyat', '360dialog'])->nullable()->after('whatsapp_id');
            }
            if (!Schema::hasColumn('businesses', 'message_quality')) {
                $table->string('message_quality')->nullable()->after('whatsapp_provider');
            }
            if (!Schema::hasColumn('businesses', 'messaging_tier')) {
                $table->string('messaging_tier')->nullable()->after('message_quality');
            }
            if (!Schema::hasColumn('businesses', 'whatsapp_settings')) {
                $table->json('whatsapp_settings')->nullable()->after('messaging_tier');
            }
            if (!Schema::hasColumn('businesses', 'whatsapp_verified_at')) {
                $table->timestamp('whatsapp_verified_at')->nullable()->after('whatsapp_settings');
            }
            if (!Schema::hasColumn('businesses', 'meta_business_verified')) {
                $table->boolean('meta_business_verified')->default(false)->after('whatsapp_verified_at');
            }
            if (!Schema::hasColumn('businesses', 'whatsapp_business_verified')) {
                $table->boolean('whatsapp_business_verified')->default(false)->after('meta_business_verified');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $columnsToCheck = [
                'whatsapp_enabled',
                'meta_business_id',
                'whatsapp_id',
                'whatsapp_provider',
                'message_quality',
                'messaging_tier',
                'whatsapp_settings',
                'whatsapp_verified_at',
                'meta_business_verified',
                'whatsapp_business_verified'
            ];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('businesses', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
