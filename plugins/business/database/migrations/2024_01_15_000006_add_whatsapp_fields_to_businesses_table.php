<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            // WhatsApp Business Integration Fields
            $table->boolean('whatsapp_enabled')->default(false)->after('is_active');
            $table->string('meta_business_id')->nullable()->after('whatsapp_enabled');
            $table->string('whatsapp_id')->nullable()->after('meta_business_id');
            $table->enum('whatsapp_provider', ['taqnyat', '360dialog'])->nullable()->after('whatsapp_id');
            $table->string('message_quality')->nullable()->after('whatsapp_provider');
            $table->string('messaging_tier')->nullable()->after('message_quality');
            $table->json('whatsapp_settings')->nullable()->after('messaging_tier');
            $table->timestamp('whatsapp_verified_at')->nullable()->after('whatsapp_settings');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $table->dropColumn([
                'whatsapp_enabled',
                'meta_business_id',
                'whatsapp_id',
                'whatsapp_provider',
                'message_quality',
                'messaging_tier',
                'whatsapp_settings',
                'whatsapp_verified_at'
            ]);
        });
    }
};
