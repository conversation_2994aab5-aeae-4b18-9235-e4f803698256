<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            // Check if columns don't already exist before adding them
            if (!Schema::hasColumn('businesses', 'taqnyat_id')) {
                $table->string('taqnyat_id')->nullable()->after('whatsapp_business_verified');
            }
            if (!Schema::hasColumn('businesses', 'taqnyat_username')) {
                $table->string('taqnyat_username')->nullable()->after('taqnyat_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('businesses', function (Blueprint $table) {
            $columnsToCheck = [
                'taqnyat_id',
                'taqnyat_username'
            ];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('businesses', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
