<?php

namespace Plugins\Business\Imports;

use Plugins\Business\Models\Contact;
use Plugins\Business\Models\Business;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;

class ContactImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use Importable, SkipsErrors, SkipsFailures;

    private $importedCount = 0;
    private $skippedCount = 0;
    private $processedRows = 0;
    private $customErrors = [];

    /**
     * Transform a row into a model
     */
    public function model(array $row)
    {
        $this->processedRows++;

        // Normalize column headers to handle variations
        $row = $this->normalizeHeaders($row);

        // Skip empty rows
        if (empty(array_filter($row))) {
            return null;
        }

        // Validate required fields
        if (empty($row['name'])) {
            $this->customErrors[] = "Row " . ($this->processedRows + 1) . ": Missing required field (name).";
            return null;
        }

        try {
            // Find the business by name or email
            $business = null;

            // Try to find business by name first
            if (!empty($row['business_name']) && trim($row['business_name']) !== '') {
                $business = Business::where('name', trim($row['business_name']))->first();
            }

            // If not found by name, try by email
            if (!$business && !empty($row['business_email']) && trim($row['business_email']) !== '') {
                $business = Business::where('email', trim($row['business_email']))->first();
            }

            // If still not found and there's only one business in the database, use it
            if (!$business) {
                $businessCount = Business::count();
                if ($businessCount === 1) {
                    $business = Business::first();
                } else {
                    $businessIdentifier = !empty($row['business_name']) ? $row['business_name'] : ($row['business_email'] ?? 'N/A');
                    if ($businessCount === 0) {
                        $this->customErrors[] = "Row " . ($this->processedRows + 1) . ": No businesses exist in the database. Please create a business first.";
                    } else {
                        $this->customErrors[] = "Row " . ($this->processedRows + 1) . ": Business not found for contact '{$row['name']}' (searched for: '{$businessIdentifier}'). Please ensure the business exists or provide correct business_name/business_email.";
                    }
                    return null;
                }
            }

            // Check if contact already exists for this business
            $existingContact = Contact::where('business_id', $business->id)
                ->where('name', $row['name'])
                ->first();

            // If email is provided, also check by email
            if (!$existingContact && !empty($row['email'])) {
                $existingContact = Contact::where('business_id', $business->id)
                    ->where('email', $row['email'])
                    ->first();
            }

            if ($existingContact) {
                // Skip existing contacts silently (don't add to errors)
                $this->skippedCount++;
                return null;
            }

            // Handle primary contact logic
            $isPrimary = filter_var($row['is_primary'] ?? false, FILTER_VALIDATE_BOOLEAN);

            // If this contact is set as primary, unset other primary contacts for this business
            if ($isPrimary) {
                Contact::where('business_id', $business->id)
                    ->where('is_primary', true)
                    ->update(['is_primary' => false]);
            }

            $contactData = [
                'business_id' => $business->id,
                'name' => $row['name'],
                'arabic_name' => !empty($row['arabic_name']) ? $row['arabic_name'] : null,
                'position' => !empty($row['position']) ? $row['position'] : null,
                'department' => !empty($row['department']) ? $row['department'] : null,
                'email' => !empty($row['email']) ? $row['email'] : null,
                'phone' => !empty($row['phone']) ? $row['phone'] : null,
                'phone_ext' => !empty($row['phone_ext']) ? $row['phone_ext'] : null,
                'phone2' => !empty($row['phone2']) ? $row['phone2'] : null,
                'phone2_ext' => !empty($row['phone2_ext']) ? $row['phone2_ext'] : null,
                'is_primary' => $isPrimary,
                'notes' => !empty($row['notes']) ? $row['notes'] : null,
            ];



            $contact = new Contact($contactData);

            $this->importedCount++;
            return $contact;

        } catch (\Exception $e) {
            $this->customErrors[] = "Row " . ($this->processedRows + 1) . ": " . $e->getMessage();
            return null;
        }
    }

    /**
     * Validation rules for each row
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'arabic_name' => 'nullable|string|max:255',
            'business_name' => 'required_without:business_email|nullable|string|max:255',
            'business_email' => 'required_without:business_name|nullable|email|max:255',
            'position' => 'nullable|string|max:255',
            'department' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'phone_ext' => 'nullable|max:10',
            'phone2' => 'nullable|string|max:20',
            'phone2_ext' => 'nullable|max:10',
            'is_primary' => 'nullable|boolean',
            'notes' => 'nullable|string',
        ];
    }

    /**
     * Custom attributes for validation error messages
     */
    public function customValidationAttributes(): array
    {
        return [
            'name' => 'Contact Name',
            'business_name' => 'Business Name',
            'business_email' => 'Business Email',
            'position' => 'Position',
            'department' => 'Department',
            'email' => 'Email Address',
            'phone' => 'Phone Number',
            'is_primary' => 'Primary Contact',
            'notes' => 'Notes',
        ];
    }

    /**
     * Get the count of imported records
     */
    public function getImportedCount(): int
    {
        return $this->importedCount;
    }

    /**
     * Get the count of skipped records
     */
    public function getSkippedCount(): int
    {
        return $this->skippedCount;
    }

    /**
     * Get the count of processed rows
     */
    public function getProcessedRows(): int
    {
        return $this->processedRows;
    }

    /**
     * Get import errors
     */
    public function getErrors(): array
    {
        // Combine validation failures and custom errors
        $allErrors = $this->customErrors;

        foreach ($this->failures() as $failure) {
            $allErrors[] = "Row {$failure->row()}: " . implode(', ', $failure->errors());
        }

        return $allErrors;
    }

    /**
     * Get import summary
     */
    public function getSummary(): array
    {
        return [
            'processed' => $this->processedRows,
            'imported' => $this->importedCount,
            'skipped' => $this->skippedCount,
            'errors' => count($this->getErrors())
        ];
    }

    /**
     * Normalize column headers to handle variations
     */
    private function normalizeHeaders(array $row): array
    {
        $normalized = [];
        $headerMap = [
            // Contact fields
            'name' => 'name',
            'contact_name' => 'name',
            'full_name' => 'name',
            'person_name' => 'name',

            // Arabic name variations
            'arabic_name' => 'arabic_name',
            'name_arabic' => 'arabic_name',
            'arabic' => 'arabic_name',
            'الاسم' => 'arabic_name',
            'الاسم_العربي' => 'arabic_name',
            'اسم_عربي' => 'arabic_name',

            'email' => 'email',
            'email_address' => 'email',
            'e_mail' => 'email',
            'e-mail' => 'email',
            'contact_email' => 'email',

            'phone' => 'phone',
            'phone_number' => 'phone',
            'telephone' => 'phone',
            'tel' => 'phone',
            'contact_phone' => 'phone',
            'primary_phone' => 'phone',

            'phone2' => 'phone2',
            'phone_2' => 'phone2',
            'second_phone' => 'phone2',
            'secondary_phone' => 'phone2',
            'alternate_phone' => 'phone2',
            'mobile' => 'phone2',
            'cell' => 'phone2',

            'phone_ext' => 'phone_ext',
            'extension' => 'phone_ext',
            'ext' => 'phone_ext',
            'phone_extension' => 'phone_ext',

            'phone2_ext' => 'phone2_ext',
            'phone_2_ext' => 'phone2_ext',
            'phone2_extension' => 'phone2_ext',
            'second_phone_ext' => 'phone2_ext',

            'position' => 'position',
            'title' => 'position',
            'job_title' => 'position',
            'role' => 'position',

            'department' => 'department',
            'dept' => 'department',
            'division' => 'department',

            'business_name' => 'business_name',
            'company_name' => 'business_name',
            'business' => 'business_name',
            'company' => 'business_name',
            'organization' => 'business_name',

            'business_email' => 'business_email',
            'company_email' => 'business_email',

            'notes' => 'notes',
            'comments' => 'notes',
            'remarks' => 'notes',

            'is_primary' => 'is_primary',
            'primary' => 'is_primary',
            'primary_contact' => 'is_primary',
        ];

        foreach ($row as $key => $value) {
            // Normalize the key (lowercase, replace spaces/dashes with underscores)
            $normalizedKey = strtolower(trim($key));
            $normalizedKey = preg_replace('/[\s\-]+/', '_', $normalizedKey);

            // Map to standard field name if exists
            if (isset($headerMap[$normalizedKey])) {
                $normalized[$headerMap[$normalizedKey]] = $value;
            } else {
                // Keep original key if no mapping found
                $normalized[$normalizedKey] = $value;
            }
        }

        return $normalized;
    }
}
