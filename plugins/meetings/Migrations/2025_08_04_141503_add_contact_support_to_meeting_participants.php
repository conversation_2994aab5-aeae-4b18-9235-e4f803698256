<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('meeting_participants', function (Blueprint $table) {
            // Make user_id nullable to support contacts
            $table->foreignId('user_id')->nullable()->change();

            // Add fields for contact participants
            $table->string('contact_email')->nullable()->after('user_id');
            $table->string('contact_name')->nullable()->after('contact_email');

            // Remove the unique constraint on meeting_id, user_id since user_id can be null
            $table->dropUnique(['meeting_id', 'user_id']);

            // Add new unique constraint that handles both users and contacts
            $table->unique(['meeting_id', 'user_id', 'contact_email'], 'unique_meeting_participant');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('meeting_participants', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique('unique_meeting_participant');

            // Restore original unique constraint
            $table->unique(['meeting_id', 'user_id']);

            // Remove contact fields
            $table->dropColumn(['contact_email', 'contact_name']);

            // Make user_id required again
            $table->foreignId('user_id')->nullable(false)->change();
        });
    }
};
