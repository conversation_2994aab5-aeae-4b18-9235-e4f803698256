<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Note: This migration is primarily for documentation purposes.
     * SQLite doesn't enforce enum constraints, so the 'recording' category
     * will work without database schema changes.
     */
    public function up(): void
    {
        // For SQLite, no schema changes needed
        // The validation in the controller handles the enum values

        // If using MySQL in production, you would need:
        // DB::statement("ALTER TABLE meeting_attachments MODIFY COLUMN category ENUM('pre_meeting', 'post_meeting', 'minutes', 'recording', 'other') DEFAULT 'other'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // For SQLite, no schema changes needed

        // If using MySQL in production, you would need:
        // DB::statement("ALTER TABLE meeting_attachments MODIFY COLUMN category ENUM('pre_meeting', 'post_meeting', 'minutes', 'other') DEFAULT 'other'");
    }
};
