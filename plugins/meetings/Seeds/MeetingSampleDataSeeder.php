<?php

namespace Plugins\Meetings\Seeds;

use Illuminate\Database\Seeder;
use Plugins\Meetings\Models\Meeting;
use Plugins\Meetings\Models\MeetingParticipant;
use Plugins\Meetings\Models\ActionItem;
use App\Models\User;
use Carbon\Carbon;

class MeetingSampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users to use as participants
        $users = User::limit(5)->get();
        
        if ($users->count() < 2) {
            $this->command->info('Not enough users found. Skipping meeting sample data.');
            return;
        }

        $creator = $users->first();

        // Create sample meetings
        $meetings = [
            [
                'title' => 'Weekly Team Standup',
                'description' => 'Weekly team synchronization meeting to discuss progress, blockers, and upcoming tasks.',
                'agenda' => '<h3>Agenda</h3><ul><li>Round-robin updates from team members</li><li>Review sprint progress</li><li>Discuss blockers and impediments</li><li>Plan for upcoming week</li></ul>',
                'scheduled_at' => Carbon::now()->addDays(1)->setTime(9, 0),
                'duration_minutes' => 60,
                'location' => 'Conference Room A',
                'status' => 'scheduled',
            ],
            [
                'title' => 'Project Kickoff Meeting',
                'description' => 'Initial meeting to discuss the new project requirements, timeline, and team assignments.',
                'agenda' => '<h3>Project Kickoff Agenda</h3><ul><li>Project overview and objectives</li><li>Timeline and milestones</li><li>Team roles and responsibilities</li><li>Resource allocation</li><li>Risk assessment</li><li>Next steps</li></ul>',
                'scheduled_at' => Carbon::now()->addDays(3)->setTime(14, 0),
                'duration_minutes' => 120,
                'meeting_link' => 'https://zoom.us/j/123456789',
                'status' => 'scheduled',
            ],
            [
                'title' => 'Monthly Review Meeting',
                'description' => 'Monthly review of team performance, achievements, and areas for improvement.',
                'agenda' => '<h3>Monthly Review</h3><ul><li>Performance metrics review</li><li>Achievement highlights</li><li>Challenges and lessons learned</li><li>Goals for next month</li><li>Team feedback session</li></ul>',
                'minutes' => '<h3>Meeting Minutes</h3><p><strong>Date:</strong> ' . Carbon::now()->subDays(5)->format('F j, Y') . '</p><p><strong>Attendees:</strong> All team members present</p><h4>Key Discussion Points:</h4><ul><li>Exceeded monthly targets by 15%</li><li>Successfully launched new feature</li><li>Need to improve communication between teams</li><li>Planning team building event</li></ul><h4>Decisions Made:</h4><ul><li>Implement weekly cross-team sync meetings</li><li>Allocate budget for team building activities</li></ul>',
                'scheduled_at' => Carbon::now()->subDays(5)->setTime(10, 0),
                'duration_minutes' => 90,
                'location' => 'Main Conference Room',
                'status' => 'completed',
            ],
            [
                'title' => 'Client Presentation',
                'description' => 'Presentation of project progress and deliverables to the client.',
                'agenda' => '<h3>Client Presentation Agenda</h3><ul><li>Welcome and introductions</li><li>Project status update</li><li>Demo of completed features</li><li>Timeline for remaining work</li><li>Q&A session</li><li>Next steps and follow-up</li></ul>',
                'scheduled_at' => Carbon::now()->addWeeks(1)->setTime(15, 30),
                'duration_minutes' => 90,
                'meeting_link' => 'https://teams.microsoft.com/l/meetup-join/19%3ameeting',
                'status' => 'scheduled',
            ],
        ];

        foreach ($meetings as $meetingData) {
            $meeting = Meeting::create([
                ...$meetingData,
                'created_by' => $creator->id,
            ]);

            // Add participants
            $participantUsers = $users->random(rand(2, min(4, $users->count())));
            foreach ($participantUsers as $user) {
                if (!$meeting->participants()->where('user_id', $user->id)->exists()) {
                    MeetingParticipant::create([
                        'meeting_id' => $meeting->id,
                        'user_id' => $user->id,
                        'status' => $user->id === $creator->id ? 'accepted' : collect(['invited', 'accepted', 'tentative'])->random(),
                    ]);
                }
            }

            // Add some action items for completed and upcoming meetings
            if (in_array($meeting->status, ['completed', 'scheduled'])) {
                $actionItems = [
                    [
                        'title' => 'Update project documentation',
                        'description' => 'Review and update all project documentation to reflect current status.',
                        'priority' => 'medium',
                        'due_date' => Carbon::now()->addDays(7),
                        'status' => 'pending',
                    ],
                    [
                        'title' => 'Prepare demo environment',
                        'description' => 'Set up and configure demo environment for client presentation.',
                        'priority' => 'high',
                        'due_date' => Carbon::now()->addDays(3),
                        'status' => 'in_progress',
                    ],
                    [
                        'title' => 'Send meeting summary',
                        'description' => 'Compile and send meeting summary to all participants.',
                        'priority' => 'low',
                        'due_date' => Carbon::now()->addDays(1),
                        'status' => $meeting->status === 'completed' ? 'completed' : 'pending',
                    ],
                ];

                foreach ($actionItems as $actionItemData) {
                    ActionItem::create([
                        ...$actionItemData,
                        'meeting_id' => $meeting->id,
                        'assigned_to' => $participantUsers->random()->id,
                        'created_by' => $creator->id,
                        'completed_at' => $actionItemData['status'] === 'completed' ? Carbon::now()->subDays(1) : null,
                    ]);
                }
            }
        }

        $this->command->info('Created ' . count($meetings) . ' sample meetings with participants and action items.');
    }
}
