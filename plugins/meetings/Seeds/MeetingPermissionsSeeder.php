<?php

namespace Plugins\Meetings\Seeds;

use Illuminate\Database\Seeder;
use App\Models\Permission;
use App\Models\Role;

class MeetingPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define meeting permissions
        $permissions = [
            [
                'name' => 'manage_meetings',
                'description' => 'Full access to manage all meetings and settings',
                'category' => 'meetings'
            ],
            [
                'name' => 'view_meetings',
                'description' => 'View meetings and participate in meetings',
                'category' => 'meetings'
            ],
            [
                'name' => 'create_meetings',
                'description' => 'Create new meetings and schedule events',
                'category' => 'meetings'
            ],
            [
                'name' => 'edit_meetings',
                'description' => 'Edit existing meetings and update details',
                'category' => 'meetings'
            ],
            [
                'name' => 'delete_meetings',
                'description' => 'Delete meetings and cancel events',
                'category' => 'meetings'
            ],
            [
                'name' => 'manage_meeting_attachments',
                'description' => 'Upload, download, and manage meeting attachments',
                'category' => 'meetings'
            ],
        ];

        // Create permissions
        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                $permissionData
            );
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    /**
     * Assign permissions to default roles
     */
    private function assignPermissionsToRoles(): void
    {
        // Admin role - full access
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $adminPermissions = [
                'manage_meetings',
                'view_meetings',
                'create_meetings',
                'edit_meetings',
                'delete_meetings',
                'manage_meeting_attachments'
            ];
            
            foreach ($adminPermissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission && !$adminRole->permissions()->where('permission_id', $permission->id)->exists()) {
                    $adminRole->permissions()->attach($permission->id);
                }
            }
        }

        // Manager role - most permissions
        $managerRole = Role::where('name', 'manager')->first();
        if ($managerRole) {
            $managerPermissions = [
                'view_meetings',
                'create_meetings',
                'edit_meetings',
                'manage_meeting_attachments'
            ];
            
            foreach ($managerPermissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission && !$managerRole->permissions()->where('permission_id', $permission->id)->exists()) {
                    $managerRole->permissions()->attach($permission->id);
                }
            }
        }

        // Employee role - basic permissions
        $employeeRole = Role::where('name', 'employee')->first();
        if ($employeeRole) {
            $employeePermissions = [
                'view_meetings',
                'create_meetings',
                'manage_meeting_attachments'
            ];
            
            foreach ($employeePermissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission && !$employeeRole->permissions()->where('permission_id', $permission->id)->exists()) {
                    $employeeRole->permissions()->attach($permission->id);
                }
            }
        }

        // User role - view only
        $userRole = Role::where('name', 'user')->first();
        if ($userRole) {
            $userPermissions = [
                'view_meetings'
            ];
            
            foreach ($userPermissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission && !$userRole->permissions()->where('permission_id', $permission->id)->exists()) {
                    $userRole->permissions()->attach($permission->id);
                }
            }
        }
    }
}
