<?php

use Illuminate\Support\Facades\Route;
use Plugins\Meetings\Controllers\MeetingController;
use Plugins\Meetings\Controllers\MeetingAttachmentController;
use Plugins\Meetings\Controllers\ActionItemController;

Route::middleware(['web', 'auth'])->group(function () {
    Route::prefix('meetings')->name('meetings.')->group(function () {
        // Main meeting routes
        Route::get('/', [MeetingController::class, 'index'])->name('index');
        Route::post('/', [MeetingController::class, 'store'])->name('store');
        Route::get('/{meeting}', [MeetingController::class, 'show'])->name('show');
        Route::put('/{meeting}', [MeetingController::class, 'update'])->name('update');
        Route::delete('/{meeting}', [MeetingController::class, 'destroy'])->name('destroy');
        
        // AJAX routes for dynamic functionality
        Route::get('/api/list', [MeetingController::class, 'apiList'])->name('api.list');
        Route::get('/api/users/search', [MeetingController::class, 'searchUsers'])->name('api.users.search');
        Route::get('/api/upload-limits', [MeetingAttachmentController::class, 'getUploadLimits'])->name('api.upload-limits');
        Route::post('/{meeting}/participants', [MeetingController::class, 'addParticipant'])->name('participants.add');
        Route::delete('/{meeting}/participants/{participant}', [MeetingController::class, 'removeParticipant'])->name('participants.remove');
        
        // Meeting attachments
        Route::prefix('{meeting}/attachments')->name('attachments.')->group(function () {
            Route::get('/test', [MeetingAttachmentController::class, 'test'])->name('test');
            Route::post('/', [MeetingAttachmentController::class, 'store'])->name('store');
            Route::get('/{attachment}/download', [MeetingAttachmentController::class, 'download'])->name('download');
            Route::get('/{attachment}/stream', [MeetingAttachmentController::class, 'stream'])->name('stream');
            Route::delete('/{attachment}', [MeetingAttachmentController::class, 'destroy'])->name('destroy');
        });
        
        // Action items
        Route::prefix('{meeting}/action-items')->name('action-items.')->group(function () {
            Route::post('/', [ActionItemController::class, 'store'])->name('store');
            Route::put('/{actionItem}', [ActionItemController::class, 'update'])->name('update');
            Route::delete('/{actionItem}', [ActionItemController::class, 'destroy'])->name('destroy');
            Route::post('/{actionItem}/toggle', [ActionItemController::class, 'toggle'])->name('toggle');
        });
    });
});
