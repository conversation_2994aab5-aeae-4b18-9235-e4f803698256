<?php

namespace Plugins\Meetings\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;


class ActionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'meeting_id',
        'title',
        'description',
        'assigned_to',
        'due_date',
        'priority',
        'status',
        'notes',
        'completed_at',
        'created_by'
    ];

    protected $casts = [
        'due_date' => 'date',
        'completed_at' => 'datetime'
    ];

    /**
     * Get the meeting this action item belongs to
     */
    public function meeting(): BelongsTo
    {
        return $this->belongsTo(Meeting::class);
    }

    /**
     * Get the user assigned to this action item
     */
    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the user who created this action item
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get action items by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending action items
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get completed action items
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get action items by priority
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get overdue action items
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now()->toDateString())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * Scope to get action items assigned to a user
     */
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * Check if action item is overdue
     */
    public function isOverdue(): bool
    {
        return $this->due_date 
            && $this->due_date < now()->toDateString() 
            && !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Check if action item is due today
     */
    public function isDueToday(): bool
    {
        return $this->due_date && $this->due_date->isToday();
    }

    /**
     * Check if action item is due this week
     */
    public function isDueThisWeek(): bool
    {
        return $this->due_date 
            && $this->due_date >= now()->startOfWeek()
            && $this->due_date <= now()->endOfWeek();
    }

    /**
     * Get the status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'pending' => 'bg-yellow-100 text-yellow-800',
            'in_progress' => 'bg-blue-100 text-blue-800',
            'completed' => 'bg-green-100 text-green-800',
            'cancelled' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get the priority badge class
     */
    public function getPriorityBadgeClassAttribute(): string
    {
        return match($this->priority) {
            'low' => 'bg-gray-100 text-gray-800',
            'medium' => 'bg-yellow-100 text-yellow-800',
            'high' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get days until due date
     */
    public function getDaysUntilDueAttribute(): ?int
    {
        if (!$this->due_date) {
            return null;
        }
        
        return now()->diffInDays($this->due_date, false);
    }

    /**
     * Get formatted due date status
     */
    public function getDueDateStatusAttribute(): string
    {
        if (!$this->due_date) {
            return 'No due date';
        }
        
        if ($this->isOverdue()) {
            $days = abs($this->days_until_due);
            return $days === 1 ? '1 day overdue' : "{$days} days overdue";
        }
        
        if ($this->isDueToday()) {
            return 'Due today';
        }
        
        $days = $this->days_until_due;
        if ($days === 1) {
            return 'Due tomorrow';
        } elseif ($days <= 7) {
            return "Due in {$days} days";
        }
        
        return 'Due ' . $this->due_date->format('M j, Y');
    }

    /**
     * Mark action item as completed
     */
    public function markCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now()
        ]);
    }

    /**
     * Mark action item as pending
     */
    public function markPending(): void
    {
        $this->update([
            'status' => 'pending',
            'completed_at' => null
        ]);
    }

    /**
     * Toggle completion status
     */
    public function toggleCompletion(): void
    {
        if ($this->status === 'completed') {
            $this->markPending();
        } else {
            $this->markCompleted();
        }
    }

    /**
     * Get available statuses
     */
    public static function getStatuses(): array
    {
        return [
            'pending' => 'Pending',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled'
        ];
    }

    /**
     * Get available priorities
     */
    public static function getPriorities(): array
    {
        return [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High'
        ];
    }
}
