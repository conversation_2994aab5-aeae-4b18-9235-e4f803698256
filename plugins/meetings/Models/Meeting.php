<?php

namespace Plugins\Meetings\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\User;
use Carbon\Carbon;

class Meeting extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'agenda',
        'minutes',
        'scheduled_at',
        'duration_minutes',
        'location',
        'meeting_link',
        'status',
        'created_by'
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'duration_minutes' => 'integer'
    ];

    /**
     * Get the user who created the meeting
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the meeting participants
     */
    public function participants(): Has<PERSON><PERSON>
    {
        return $this->hasMany(MeetingParticipant::class);
    }

    /**
     * Get the meeting attachments
     */
    public function attachments(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(MeetingAttachment::class);
    }

    /**
     * Get the action items for this meeting
     */
    public function actionItems(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(ActionItem::class);
    }

    /**
     * Scope to get upcoming meetings
     */
    public function scopeUpcoming($query)
    {
        return $query->where('scheduled_at', '>', now());
    }

    /**
     * Scope to get past meetings
     */
    public function scopePast($query)
    {
        return $query->where('scheduled_at', '<=', now());
    }

    /**
     * Scope to get meetings by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get meetings for a specific user (creator or participant)
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where(function($q) use ($userId) {
            $q->where('created_by', $userId)
              ->orWhereHas('participants', function($subQ) use ($userId) {
                  $subQ->where('user_id', $userId);
              });
        });
    }

    /**
     * Check if the meeting is upcoming
     */
    public function isUpcoming(): bool
    {
        return $this->scheduled_at > now();
    }

    /**
     * Check if the meeting is past
     */
    public function isPast(): bool
    {
        return $this->scheduled_at <= now();
    }

    /**
     * Check if the meeting is overdue (past scheduled time but not completed)
     */
    public function isOverdue(): bool
    {
        return $this->isPast() && $this->status !== 'completed' && $this->status !== 'cancelled';
    }

    /**
     * Get the meeting end time
     */
    public function getEndTimeAttribute(): Carbon
    {
        return $this->scheduled_at->addMinutes($this->duration_minutes);
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;
        
        if ($hours > 0) {
            return $minutes > 0 ? "{$hours}h {$minutes}m" : "{$hours}h";
        }
        
        return "{$minutes}m";
    }

    /**
     * Get the meeting status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'scheduled' => 'bg-blue-100 text-blue-800',
            'in_progress' => 'bg-yellow-100 text-yellow-800',
            'completed' => 'bg-green-100 text-green-800',
            'cancelled' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get participant count
     */
    public function getParticipantCountAttribute(): int
    {
        return $this->participants()->count();
    }

    /**
     * Get attachment count
     */
    public function getAttachmentCountAttribute(): int
    {
        return $this->attachments()->count();
    }

    /**
     * Get action item count
     */
    public function getActionItemCountAttribute(): int
    {
        return $this->actionItems()->count();
    }

    /**
     * Get pending action item count
     */
    public function getPendingActionItemCountAttribute(): int
    {
        return $this->actionItems()->where('status', 'pending')->count();
    }

    /**
     * Check if user can edit this meeting
     */
    public function canEdit(User $user): bool
    {
        return $user->id === $this->created_by || $user->hasPermission('manage_meetings');
    }

    /**
     * Check if user can view this meeting
     */
    public function canView(User $user): bool
    {
        return $user->id === $this->created_by 
            || $this->participants()->where('user_id', $user->id)->exists()
            || $user->hasPermission('manage_meetings');
    }

    /**
     * Get available statuses
     */
    public static function getStatuses(): array
    {
        return [
            'scheduled' => 'Scheduled',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled'
        ];
    }
}
