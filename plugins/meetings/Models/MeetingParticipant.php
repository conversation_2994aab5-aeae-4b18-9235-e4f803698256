<?php

namespace Plugins\Meetings\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

class MeetingParticipant extends Model
{
    use HasFactory;

    protected $fillable = [
        'meeting_id',
        'user_id',
        'contact_email',
        'contact_name',
        'status',
        'notes',
        'responded_at'
    ];

    protected $casts = [
        'responded_at' => 'datetime'
    ];

    /**
     * Get the meeting this participant belongs to
     */
    public function meeting(): BelongsTo
    {
        return $this->belongsTo(Meeting::class);
    }

    /**
     * Get the user who is the participant
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get participants by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get accepted participants
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    /**
     * Scope to get declined participants
     */
    public function scopeDeclined($query)
    {
        return $query->where('status', 'declined');
    }

    /**
     * Scope to get pending participants (invited but not responded)
     */
    public function scopePending($query)
    {
        return $query->where('status', 'invited');
    }

    /**
     * Get the status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'invited' => 'bg-gray-100 text-gray-800',
            'accepted' => 'bg-green-100 text-green-800',
            'declined' => 'bg-red-100 text-red-800',
            'tentative' => 'bg-yellow-100 text-yellow-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get available statuses
     */
    public static function getStatuses(): array
    {
        return [
            'invited' => 'Invited',
            'accepted' => 'Accepted',
            'declined' => 'Declined',
            'tentative' => 'Tentative'
        ];
    }

    /**
     * Update participant response
     */
    public function updateResponse(string $status, ?string $notes = null): void
    {
        $this->update([
            'status' => $status,
            'notes' => $notes,
            'responded_at' => now()
        ]);
    }

    /**
     * Get participant name (user or contact)
     */
    public function getParticipantName(): string
    {
        if ($this->user) {
            return $this->user->name;
        }

        return $this->contact_name ?? 'Unknown';
    }

    /**
     * Get participant email (user or contact)
     */
    public function getParticipantEmail(): ?string
    {
        if ($this->user) {
            return $this->user->email;
        }

        return $this->contact_email;
    }

    /**
     * Check if participant is a contact (not a system user)
     */
    public function isContact(): bool
    {
        return $this->user_id === null && !empty($this->contact_email);
    }

    /**
     * Check if participant is a system user
     */
    public function isUser(): bool
    {
        return $this->user_id !== null;
    }
}
