<?php

namespace Plugins\Meetings\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Models\User;

class MeetingAttachment extends Model
{
    use HasFactory;

    protected $fillable = [
        'meeting_id',
        'filename',
        'original_filename',
        'file_path',
        'mime_type',
        'file_size',
        'attachment_type',
        'category',
        'description',
        'is_public',
        'uploaded_by',
        'download_count'
    ];

    protected $casts = [
        'file_size' => 'integer',
        'is_public' => 'boolean',
        'download_count' => 'integer'
    ];

    /**
     * Get the meeting this attachment belongs to
     */
    public function meeting(): BelongsTo
    {
        return $this->belongsTo(Meeting::class);
    }

    /**
     * Get the user who uploaded this attachment
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Scope to get attachments by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('attachment_type', $type);
    }

    /**
     * Scope to get attachments by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get public attachments
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Check if file exists
     */
    public function fileExists(): bool
    {
        return Storage::disk('local')->exists($this->file_path);
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get file extension
     */
    public function getFileExtensionAttribute(): string
    {
        return pathinfo($this->original_filename, PATHINFO_EXTENSION);
    }

    /**
     * Get file icon class based on type
     */
    public function getFileIconClassAttribute(): string
    {
        return match($this->attachment_type) {
            'document' => $this->getDocumentIconClass(),
            'image' => 'fas fa-image text-green-500',
            'audio' => 'fas fa-music text-purple-500',
            'video' => 'fas fa-video text-red-500',
            default => 'fas fa-file text-gray-500'
        };
    }

    /**
     * Get document icon class based on extension
     */
    private function getDocumentIconClass(): string
    {
        $extension = strtolower($this->file_extension);
        
        return match($extension) {
            'pdf' => 'fas fa-file-pdf text-red-500',
            'doc', 'docx' => 'fas fa-file-word text-blue-500',
            'xls', 'xlsx' => 'fas fa-file-excel text-green-500',
            'ppt', 'pptx' => 'fas fa-file-powerpoint text-orange-500',
            'txt' => 'fas fa-file-alt text-gray-500',
            'zip', 'rar' => 'fas fa-file-archive text-yellow-500',
            default => 'fas fa-file text-gray-500'
        };
    }

    /**
     * Determine file type from extension
     */
    public static function determineFileType(string $extension): string
    {
        $extension = strtolower($extension);
        
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        $audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'm4a'];
        $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
        $documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
        
        if (in_array($extension, $imageExtensions)) {
            return 'image';
        } elseif (in_array($extension, $audioExtensions)) {
            return 'audio';
        } elseif (in_array($extension, $videoExtensions)) {
            return 'video';
        } elseif (in_array($extension, $documentExtensions)) {
            return 'document';
        }
        
        return 'other';
    }

    /**
     * Create attachment from uploaded file
     */
    public static function createFromUpload($file, Meeting $meeting, array $options = []): self
    {
        $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
        $directory = 'meeting-attachments/' . $meeting->id;

        Log::info('About to store file', [
            'filename' => $filename,
            'directory' => $directory,
            'original_filename' => $file->getClientOriginalName(),
            'temp_path' => $file->getPathname(),
            'temp_file_exists' => file_exists($file->getPathname()),
            'storage_disk_path' => Storage::disk('local')->path($directory),
            'storage_dir_exists' => Storage::disk('local')->exists($directory)
        ]);

        // Ensure directory exists using Storage facade
        if (!Storage::disk('local')->exists($directory)) {
            Log::info('Creating directory using Storage facade', ['directory' => $directory]);
            Storage::disk('local')->makeDirectory($directory);
        }

        $path = $file->storeAs($directory, $filename, 'local');

        Log::info('File storage result', [
            'returned_path' => $path,
            'expected_path' => $directory . '/' . $filename,
            'storage_disk_path' => Storage::disk('local')->path($path),
            'file_exists_after_store' => $path ? Storage::disk('local')->exists($path) : false,
            'file_size_on_disk' => $path && Storage::disk('local')->exists($path) ? Storage::disk('local')->size($path) : 'N/A'
        ]);

        if (!$path) {
            Log::error('File storage failed - storeAs returned false');
            throw new \Exception('Failed to store uploaded file');
        }

        if (!Storage::disk('local')->exists($path)) {
            Log::error('File was not actually stored', [
                'expected_path' => Storage::disk('local')->path($path),
                'storage_root' => Storage::disk('local')->path('')
            ]);
            throw new \Exception('File storage verification failed');
        }

        return static::create([
            'meeting_id' => $meeting->id,
            'filename' => $filename,
            'original_filename' => $file->getClientOriginalName(),
            'file_path' => $path,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'attachment_type' => static::determineFileType($file->getClientOriginalExtension()),
            'category' => $options['category'] ?? 'other',
            'description' => $options['description'] ?? null,
            'is_public' => $options['is_public'] ?? false,
            'uploaded_by' => auth()->id(),
        ]);
    }

    /**
     * Increment download count
     */
    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    /**
     * Check if user can access this attachment
     */
    public function canAccess(User $user): bool
    {
        return $this->is_public 
            || $user->id === $this->uploaded_by
            || $this->meeting->canView($user);
    }

    /**
     * Get available categories
     */
    public static function getCategories(): array
    {
        return [
            'pre_meeting' => 'Pre-meeting',
            'post_meeting' => 'Post-meeting',
            'minutes' => 'Meeting Minutes',
            'other' => 'Other'
        ];
    }

    /**
     * Get available attachment types
     */
    public static function getAttachmentTypes(): array
    {
        return [
            'document' => 'Document',
            'image' => 'Image',
            'audio' => 'Audio',
            'video' => 'Video',
            'other' => 'Other'
        ];
    }
}
