<?php

namespace Plugins\Meetings\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Plugins\Meetings\Models\Meeting;
use Plugins\Meetings\Models\ActionItem;

class ActionItemController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            $action = $request->route()->getActionMethod();
            $requiredPermission = $this->getRequiredPermission($action);

            if ($requiredPermission && !$user->hasPermission($requiredPermission)) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Get required permission for action
     */
    private function getRequiredPermission(string $action): ?string
    {
        return match($action) {
            'store' => 'create_meetings',
            'update', 'toggle' => 'edit_meetings',
            'destroy' => 'delete_meetings',
            default => 'view_meetings'
        };
    }

    /**
     * Store a newly created action item
     */
    public function store(Request $request, Meeting $meeting): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'assigned_to' => 'nullable|exists:users,id',
            'due_date' => 'nullable|date|after_or_equal:today',
            'priority' => 'nullable|in:low,medium,high',
        ]);

        try {
            $actionItem = $meeting->actionItems()->create([
                'title' => $request->title,
                'description' => $request->description,
                'assigned_to' => $request->assigned_to,
                'due_date' => $request->due_date,
                'priority' => $request->priority ?? 'medium',
                'status' => 'pending',
                'created_by' => auth()->id(),
            ]);

            Log::info("Action item created for meeting '{$meeting->title}'", [
                'user' => auth()->user()->email,
                'meeting_id' => $meeting->id,
                'action_item_id' => $actionItem->id,
                'title' => $actionItem->title,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Action item created successfully!',
                'action_item' => $actionItem->load(['assignee', 'creator'])
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to create action item: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to create action item: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified action item
     */
    public function update(Request $request, Meeting $meeting, ActionItem $actionItem): JsonResponse
    {
        // Ensure action item belongs to meeting
        if ($actionItem->meeting_id !== $meeting->id) {
            return response()->json([
                'success' => false,
                'message' => 'Action item does not belong to this meeting.'
            ], 400);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'assigned_to' => 'nullable|exists:users,id',
            'due_date' => 'nullable|date',
            'priority' => 'nullable|in:low,medium,high',
            'status' => 'nullable|in:pending,in_progress,completed,cancelled',
            'notes' => 'nullable|string',
        ]);

        try {
            $oldStatus = $actionItem->status;
            
            $actionItem->update($request->only([
                'title', 'description', 'assigned_to', 'due_date', 
                'priority', 'status', 'notes'
            ]));

            // Update completed_at timestamp if status changed to completed
            if ($request->status === 'completed' && $oldStatus !== 'completed') {
                $actionItem->update(['completed_at' => now()]);
            } elseif ($request->status !== 'completed' && $oldStatus === 'completed') {
                $actionItem->update(['completed_at' => null]);
            }

            Log::info("Action item updated for meeting '{$meeting->title}'", [
                'user' => auth()->user()->email,
                'meeting_id' => $meeting->id,
                'action_item_id' => $actionItem->id,
                'title' => $actionItem->title,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Action item updated successfully!',
                'action_item' => $actionItem->load(['assignee', 'creator'])
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to update action item: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update action item: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified action item
     */
    public function destroy(Meeting $meeting, ActionItem $actionItem): JsonResponse
    {
        try {
            // Ensure action item belongs to meeting
            if ($actionItem->meeting_id !== $meeting->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Action item does not belong to this meeting.'
                ], 400);
            }

            $actionItemTitle = $actionItem->title;
            $actionItem->delete();

            Log::info("Action item deleted from meeting '{$meeting->title}'", [
                'user' => auth()->user()->email,
                'meeting_id' => $meeting->id,
                'action_item_id' => $actionItem->id,
                'title' => $actionItemTitle,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Action item deleted successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to delete action item: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete action item: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle action item completion status
     */
    public function toggle(Meeting $meeting, ActionItem $actionItem): JsonResponse
    {
        try {
            // Ensure action item belongs to meeting
            if ($actionItem->meeting_id !== $meeting->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Action item does not belong to this meeting.'
                ], 400);
            }

            $actionItem->toggleCompletion();

            Log::info("Action item toggled for meeting '{$meeting->title}'", [
                'user' => auth()->user()->email,
                'meeting_id' => $meeting->id,
                'action_item_id' => $actionItem->id,
                'new_status' => $actionItem->status,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Action item status updated successfully!',
                'action_item' => $actionItem->load(['assignee', 'creator'])
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to toggle action item: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to update action item: ' . $e->getMessage()
            ], 500);
        }
    }
}
