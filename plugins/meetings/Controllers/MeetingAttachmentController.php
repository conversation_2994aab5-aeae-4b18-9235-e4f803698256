<?php

namespace Plugins\Meetings\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Plugins\Meetings\Models\Meeting;
use Plugins\Meetings\Models\MeetingAttachment;

class MeetingAttachmentController extends Controller
{
    public function __construct()
    {
        // Apply middleware to check permissions
        $this->middleware(function ($request, $next) {
            $user = auth()->user();

            if (!$user || !$user->role) {
                abort(403, 'Access denied. No role assigned.');
            }

            if (!$user->hasPermission('manage_meeting_attachments')) {
                abort(403, 'Access denied. Insufficient permissions.');
            }

            return $next($request);
        });
    }

    /**
     * Store a newly uploaded attachment
     */
    public function store(Request $request, Meeting $meeting): JsonResponse
    {
        Log::info('MeetingAttachmentController::store called', [
            'meeting_id' => $meeting->id,
            'user_id' => auth()->id(),
            'request_data' => $request->except(['file']),
            'has_file' => $request->hasFile('file'),
            'file_info' => $request->hasFile('file') ? [
                'name' => $request->file('file')->getClientOriginalName(),
                'size' => $request->file('file')->getSize(),
                'mime' => $request->file('file')->getMimeType(),
            ] : null
        ]);

        // Determine validation rules based on attachment type
        $validationRules = [
            'category' => 'nullable|in:pre_meeting,post_meeting,minutes,other,recording',
            'description' => 'nullable|string|max:500',
            'is_public' => 'boolean',
            'attachment_type' => 'nullable|in:document,image,video,audio,other',
        ];

        // We use fixed limits: 500MB for videos, 2MB for other files

        // Determine if this is a video upload by checking category or file extension
        $isVideoUpload = $request->input('category') === 'recording' ||
                        $request->input('attachment_type') === 'video';

        // Also check file extension if uploaded
        if (!$isVideoUpload && $request->hasFile('file')) {
            $extension = strtolower($request->file('file')->getClientOriginalExtension());
            $videoExtensions = ['mp4', 'avi', 'mov', 'webm'];
            $isVideoUpload = in_array($extension, $videoExtensions);
        }

        if ($isVideoUpload) {
            // Allow 500MB for video files (500 * 1024 = 512000 KB)
            // Include various MIME types for WebM and other video formats
            $validationRules['file'] = "required|file|max:512000|mimetypes:video/mp4,video/avi,video/quicktime,video/webm,video/x-matroska"; // 500MB limit
        } else {
            // For other files, use a smaller limit (2MB)
            $validationRules['file'] = 'required|file|max:2048';
        }

        // Log the validation rules for debugging
        Log::info('Upload validation rules', [
            'rules' => $validationRules,
            'is_video' => $isVideoUpload,
            'category' => $request->input('category'),
            'attachment_type' => $request->input('attachment_type')
        ]);

        Log::info('About to validate request');

        try {
            $request->validate($validationRules);
            Log::info('Validation passed, proceeding with upload');
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation failed', [
                'errors' => $e->errors(),
                'file_info' => $request->hasFile('file') ? [
                    'name' => $request->file('file')->getClientOriginalName(),
                    'size' => $request->file('file')->getSize(),
                    'mime' => $request->file('file')->getMimeType(),
                    'extension' => $request->file('file')->getClientOriginalExtension(),
                ] : 'No file'
            ]);
            throw $e;
        }

        try {
            $file = $request->file('file');

            Log::info('Creating attachment from upload', [
                'filename' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'extension' => $file->getClientOriginalExtension(),
                'temp_path' => $file->getPathname(),
                'is_valid' => $file->isValid(),
                'error' => $file->getError()
            ]);

            // Check if file upload was successful
            if (!$file->isValid()) {
                Log::error('File upload validation failed', [
                    'error_code' => $file->getError(),
                    'error_message' => $file->getErrorMessage(),
                    'filename' => $file->getClientOriginalName()
                ]);
                throw new \Exception('File upload failed: ' . $file->getErrorMessage());
            }

            // Check if temp file exists
            if (!file_exists($file->getPathname())) {
                Log::error('Temporary file does not exist', [
                    'temp_path' => $file->getPathname(),
                    'filename' => $file->getClientOriginalName()
                ]);
                throw new \Exception('Temporary file not found');
            }

            Log::info('About to create attachment from upload');

            $attachment = MeetingAttachment::createFromUpload(
                $file,
                $meeting,
                [
                    'category' => $request->input('category', 'other'),
                    'description' => $request->input('description'),
                    'is_public' => $request->boolean('is_public', false),
                ]
            );

            Log::info('Attachment created, checking if file was stored', [
                'attachment_id' => $attachment->id,
                'file_path' => $attachment->file_path,
                'full_path' => Storage::disk('local')->path($attachment->file_path),
                'file_exists' => $attachment->fileExists()
            ]);

            Log::info("Attachment uploaded successfully to meeting '{$meeting->title}'", [
                'user' => auth()->user()->email,
                'meeting_id' => $meeting->id,
                'attachment_id' => $attachment->id,
                'filename' => $attachment->original_filename,
                'attachment_type' => $attachment->attachment_type,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully.',
                'attachment' => $attachment->load('uploader')
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to upload attachment", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'meeting_id' => $meeting->id,
                'user' => auth()->user()->email ?? 'unknown'
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download an attachment
     */
    public function download(Meeting $meeting, MeetingAttachment $attachment): BinaryFileResponse
    {
        // Ensure attachment belongs to meeting
        if ($attachment->meeting_id !== $meeting->id) {
            abort(404);
        }

        // Check if user can access this attachment
        if (!$attachment->canAccess(auth()->user())) {
            abort(403, 'You do not have permission to access this file.');
        }

        // Check if file exists
        if (!$attachment->fileExists()) {
            abort(404, 'File not found.');
        }

        $attachment->incrementDownloadCount();

        // Use Storage facade to get the correct path for the local disk
        $filePath = Storage::disk('local')->path($attachment->file_path);

        return response()->download(
            $filePath,
            $attachment->original_filename
        );
    }

    /**
     * Stream a video attachment for inline playback
     */
    public function stream(Meeting $meeting, MeetingAttachment $attachment)
    {
        // Ensure attachment belongs to meeting
        if ($attachment->meeting_id !== $meeting->id) {
            abort(404);
        }

        // Check if user can access this attachment
        if (!$attachment->canAccess(auth()->user())) {
            abort(403, 'You do not have permission to access this file.');
        }

        // Check if file exists
        if (!$attachment->fileExists()) {
            abort(404, 'File not found.');
        }

        // Only allow streaming for video files
        if ($attachment->attachment_type !== 'video') {
            abort(404, 'File is not a video.');
        }

        // Use Storage facade to get the correct path for the local disk
        $filePath = Storage::disk('local')->path($attachment->file_path);
        $fileSize = filesize($filePath);
        $mimeType = $attachment->mime_type;

        // Handle range requests for video streaming
        $headers = [
            'Content-Type' => $mimeType,
            'Accept-Ranges' => 'bytes',
            'Content-Length' => $fileSize,
        ];

        // Check if this is a range request
        $range = request()->header('Range');
        if ($range) {
            // Parse range header
            preg_match('/bytes=(\d+)-(\d*)/', $range, $matches);
            $start = intval($matches[1]);
            $end = $matches[2] ? intval($matches[2]) : $fileSize - 1;

            $length = $end - $start + 1;

            $headers['Content-Range'] = "bytes $start-$end/$fileSize";
            $headers['Content-Length'] = $length;

            $file = fopen($filePath, 'rb');
            fseek($file, $start);
            $data = fread($file, $length);
            fclose($file);

            return response($data, 206, $headers);
        }

        // Return full file for non-range requests
        return response()->file($filePath, $headers);
    }

    /**
     * Remove the specified attachment
     */
    public function destroy(Meeting $meeting, MeetingAttachment $attachment): JsonResponse
    {
        try {
            // Ensure attachment belongs to meeting
            if ($attachment->meeting_id !== $meeting->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Attachment does not belong to this meeting.'
                ], 400);
            }

            // Check if user can delete this attachment
            $user = auth()->user();
            if ($attachment->uploaded_by !== $user->id && !$user->hasPermission('manage_meetings')) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to delete this attachment.'
                ], 403);
            }

            $filename = $attachment->original_filename;

            // Delete file from storage
            if ($attachment->fileExists()) {
                Storage::disk('local')->delete($attachment->file_path);
            }

            // Delete attachment record
            $attachment->delete();

            Log::info("Attachment deleted from meeting '{$meeting->title}'", [
                'user' => $user->email,
                'meeting_id' => $meeting->id,
                'attachment_id' => $attachment->id,
                'filename' => $filename,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Attachment deleted successfully!'
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to delete attachment: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete attachment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Parse PHP size string (like "2M", "8M") to bytes
     */
    private function parseSize(string $size): int
    {
        $size = trim($size);
        $last = strtolower($size[strlen($size) - 1]);
        $size = (int) $size;

        switch ($last) {
            case 'g':
                $size *= 1024 * 1024 * 1024; // GB to bytes
                break;
            case 'm':
                $size *= 1024 * 1024; // MB to bytes
                break;
            case 'k':
                $size *= 1024; // KB to bytes
                break;
        }

        return $size;
    }

    /**
     * Get server upload limits for frontend validation
     */
    public function getUploadLimits(): JsonResponse
    {
        // For video files, we allow 500MB regardless of PHP limits
        $videoMaxSize = 500 * 1024 * 1024; // 500MB in bytes
        $documentMaxSize = 2 * 1024 * 1024; // 2MB in bytes

        return response()->json([
            'success' => true,
            'limits' => [
                'video_max_file_size' => $videoMaxSize,
                'video_max_file_size_mb' => 500,
                'document_max_file_size' => $documentMaxSize,
                'document_max_file_size_mb' => 2,
                'php_upload_max_filesize' => ini_get('upload_max_filesize'),
                'php_post_max_size' => ini_get('post_max_size'),
            ]
        ]);
    }

    /**
     * Test endpoint to verify route is working
     */
    public function test(Request $request, Meeting $meeting): JsonResponse
    {
        Log::info('Test endpoint called', [
            'meeting_id' => $meeting->id,
            'user_id' => auth()->id(),
            'method' => $request->method()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Test endpoint working',
            'meeting_id' => $meeting->id,
            'user_id' => auth()->id()
        ]);
    }
}
