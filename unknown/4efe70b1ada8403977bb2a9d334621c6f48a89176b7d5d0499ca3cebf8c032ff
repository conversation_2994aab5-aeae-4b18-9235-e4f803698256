<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('business_id')->constrained('businesses')->onDelete('cascade');
            
            // Notification channels
            $table->boolean('email_enabled')->default(true);
            $table->boolean('sms_enabled')->default(false);
            $table->boolean('whatsapp_enabled')->default(false);
            $table->boolean('in_app_enabled')->default(true);
            
            // Notification frequency
            $table->enum('frequency', [
                'immediate',
                'hourly',
                'daily',
                'weekly',
                'never'
            ])->default('immediate');
            
            // Activity type preferences (JSON array of enabled activity types)
            $table->json('activity_types')->nullable(); // ['comment', 'document_upload', 'status_change', etc.]
            
            // Severity level threshold
            $table->enum('min_severity', [
                'info',
                'warning',
                'important',
                'critical'
            ])->default('info');
            
            // Contact information for notifications
            $table->string('notification_email')->nullable();
            $table->string('notification_phone')->nullable();
            $table->string('notification_whatsapp')->nullable();
            
            // Quiet hours
            $table->time('quiet_hours_start')->nullable();
            $table->time('quiet_hours_end')->nullable();
            $table->json('quiet_days')->nullable(); // Array of day numbers (0=Sunday, 6=Saturday)
            
            // Last notification sent (to prevent spam)
            $table->timestamp('last_notification_sent')->nullable();
            
            $table->timestamps();
            
            // Unique constraint - one preference record per user per business
            $table->unique(['user_id', 'business_id']);
            
            // Indexes
            $table->index(['user_id']);
            $table->index(['business_id']);
            $table->index(['frequency']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_notification_preferences');
    }
};
