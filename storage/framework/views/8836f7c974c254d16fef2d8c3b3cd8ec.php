<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Validation Errors -->
    <?php if($errors->any()): ?>
        <div class="bg-red-100 dark:bg-red-900 border border-red-400 text-red-700 px-4 py-3 rounded relative transition duration-150 ease-in-out" role="alert">
            <strong class="font-bold">Please fix the following errors:</strong>
            <ul class="mt-2 list-disc list-inside">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    <?php endif; ?>
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Permission Management</h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">Manage system permissions and access controls</p>
        </div>
        <a href="<?php echo e(route('permissions.create')); ?>" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
            <i class="fas fa-plus mr-2"></i>
            Add Permission
        </a>
    </div>

    <!-- Permissions Table -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md transition duration-150 ease-in-out">
        <div class="px-4 py-5 sm:p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                Permission
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                Plugin
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                Roles Count
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                Description
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">
                                Created
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                        <?php $__empty_1 = true; $__currentLoopData = $permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 dark:bg-gray-700 transition duration-150 ease-in-out">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <i class="fas fa-key text-2xl text-purple-500"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($permission->display_name); ?></div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($permission->name); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($permission->plugin ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'); ?> transition duration-150 ease-in-out">
                                        <i class="fas <?php echo e($permission->plugin ? 'fa-puzzle-piece' : 'fa-cog'); ?> mr-1"></i>
                                        <?php echo e($permission->plugin_display_name); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 transition duration-150 ease-in-out">
                                        <?php echo e($permission->roles_count); ?> <?php echo e(Str::plural('role', $permission->roles_count)); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 dark:text-white"><?php echo e($permission->description ?: 'No description'); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                    <?php echo e($permission->created_at->format('M d, Y')); ?>

                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center justify-end space-x-2">
                                        <a href="<?php echo e(route('permissions.show', $permission)); ?>" class="text-primary-600 hover:text-primary-900" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('permissions.edit', $permission)); ?>" class="text-yellow-600 hover:text-yellow-900" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php
                                            $systemPermissions = ['manage_users', 'manage_roles', 'manage_permissions', 'manage_plugins', 'view_dashboard', 'manage_navigation', 'view_navigation'];
                                        ?>
                                        <?php if(!in_array($permission->name, $systemPermissions) && $permission->roles_count == 0): ?>
                                            <form method="POST" action="<?php echo e(route('permissions.destroy', $permission)); ?>" class="inline" onsubmit="return confirm('Are you sure you want to delete the permission \'<?php echo e($permission->display_name); ?>\' (<?php echo e($permission->name); ?>)?\n\nThis action cannot be undone.')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete Permission">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        <?php elseif(in_array($permission->name, $systemPermissions)): ?>
                                            <span class="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-not-allowed" title="Cannot delete system permissions">
                                                <i class="fas fa-trash"></i>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 cursor-not-allowed" title="Cannot delete permission assigned to <?php echo e($permission->roles_count); ?> <?php echo e(Str::plural('role', $permission->roles_count)); ?>">
                                                <i class="fas fa-trash"></i>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-center">
                                    No permissions found.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($permissions->hasPages()): ?>
                <div class="mt-6">
                    <?php echo e($permissions->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-key text-2xl text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Permissions</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($permissions->total()); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition duration-150 ease-in-out">
                <div class="text-sm">
                    <a href="<?php echo e(route('roles.index')); ?>" class="font-medium text-primary-700 hover:text-primary-900">
                        Manage Roles <span aria-hidden="true">&rarr;</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-shield-alt text-2xl text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Assigned to Roles</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($totalAssignedToRoles); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition duration-150 ease-in-out">
                <div class="text-sm">
                    <a href="<?php echo e(route('users.index')); ?>" class="font-medium text-primary-700 hover:text-primary-900">
                        Manage Users <span aria-hidden="true">&rarr;</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-cog text-2xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">System Permissions</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($systemPermissionsCount); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition duration-150 ease-in-out">
                <div class="text-sm">
                    <a href="<?php echo e(route('permissions.create')); ?>" class="font-medium text-primary-700 hover:text-primary-900">
                        Add New Permission <span aria-hidden="true">&rarr;</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-trash text-2xl <?php echo e($deletableCount > 0 ? 'text-red-600 dark:text-red-400' : 'text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500'); ?>"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Deletable Permissions</dt>
                            <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($deletableCount); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition duration-150 ease-in-out">
                <div class="text-sm">
                    <?php if($deletableCount > 0): ?>
                        <span class="font-medium text-red-700">
                            <?php echo e($deletableCount); ?> <?php echo e(Str::plural('permission', $deletableCount)); ?> can be deleted
                        </span>
                    <?php else: ?>
                        <span class="font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                            No permissions available for deletion
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/users/Views/permissions/index.blade.php ENDPATH**/ ?>