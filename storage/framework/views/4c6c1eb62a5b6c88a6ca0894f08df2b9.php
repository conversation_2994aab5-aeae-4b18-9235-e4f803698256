<?php $__env->startSection('content'); ?>
<style>
    /* Quill Editor Container Styling */
    .quill-editor-container {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        height: auto;
    }

    .quill-editor-container:focus-within {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-1px);
    }

    /* Quill Editor Toolbar Styling */
    .ql-toolbar {
        border: 1px solid #e5e7eb !important;
        border-bottom: 1px solid #d1d5db !important;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
        padding: 16px 20px !important;
        border-radius: 12px 12px 0 0 !important;
    }

    .dark .ql-toolbar {
        border: 1px solid #4b5563 !important;
        border-bottom: 1px solid #374151 !important;
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%) !important;
    }

    /* Quill Editor Content Styling */
    .ql-editor {
        border: 1px solid #e5e7eb !important;
        border-top: none !important;
        border-radius: 0 0 12px 12px !important;
        min-height: 150px !important;
        padding: 20px !important;
        font-size: 14px !important;
        line-height: 1.6 !important;
        background: #ffffff !important;
    }

    .dark .ql-editor {
        border: 1px solid #4b5563 !important;
        border-top: none !important;
        background: #1f2937 !important;
        color: #f9fafb !important;
    }
</style>
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Meetings</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">Manage your meetings and events</p>
        </div>
        <button onclick="showCreateModal()" 
                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg flex items-center transition duration-150 ease-in-out">
            <i class="fas fa-plus mr-2"></i>
            New Meeting
        </button>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition duration-150 ease-in-out">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900 transition duration-150 ease-in-out">
                    <i class="fas fa-calendar-alt text-blue-600 dark:text-blue-400"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Meetings</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e($stats['total']); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition duration-150 ease-in-out">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900 transition duration-150 ease-in-out">
                    <i class="fas fa-clock text-green-600 dark:text-green-400"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Upcoming</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e($stats['upcoming']); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition duration-150 ease-in-out">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900 transition duration-150 ease-in-out">
                    <i class="fas fa-history text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Past</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e($stats['past']); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 transition duration-150 ease-in-out">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900 transition duration-150 ease-in-out">
                    <i class="fas fa-tasks text-purple-600 dark:text-purple-400"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">With Action Items</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?php echo e($stats['with_action_items']); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6 transition duration-150 ease-in-out">
        <!-- Search Bar -->
        <div class="mb-4">
            <label for="meetingSearch" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Meetings</label>
            <div class="relative">
                <input type="text" id="meetingSearch" placeholder="Search in titles, descriptions, agenda, minutes, participants..."
                       class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 pl-10 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400 dark:text-gray-500"></i>
                </div>
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button onclick="clearSearch()" id="clearSearchBtn" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 hidden">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="mt-2 flex flex-wrap gap-2">
                <span class="text-xs text-gray-500 dark:text-gray-400">Search in:</span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 transition duration-150 ease-in-out">
                    <i class="fas fa-heading mr-1"></i>Titles
                </span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 transition duration-150 ease-in-out">
                    <i class="fas fa-align-left mr-1"></i>Descriptions
                </span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 transition duration-150 ease-in-out">
                    <i class="fas fa-list mr-1"></i>Agenda
                </span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 transition duration-150 ease-in-out">
                    <i class="fas fa-file-alt mr-1"></i>Minutes
                </span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 transition duration-150 ease-in-out">
                    <i class="fas fa-users mr-1"></i>Participants
                </span>
            </div>
        </div>

        <!-- Filters -->
        <div class="flex flex-wrap gap-4 items-center">
            <div>
                <label for="statusFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                <select id="statusFilter" class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
                    <option value="all">All Meetings</option>
                    <option value="upcoming">Upcoming</option>
                    <option value="past">Past</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>

            <div>
                <label for="dateFrom" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">From Date</label>
                <input type="date" id="dateFrom" class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
            </div>

            <div>
                <label for="dateTo" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">To Date</label>
                <input type="date" id="dateTo" class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
            </div>

            <div class="flex items-end">
                <button onclick="applyFilters()" class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-filter mr-2"></i>Apply Filters
                </button>
            </div>
        </div>
    </div>

    <!-- Meetings List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow transition duration-150 ease-in-out">
        <div class="p-6">
            <div id="meetingsContainer">
                <!-- Meetings will be rendered here -->
            </div>
        </div>
    </div>
</div>

<!-- Meeting Modal -->
<div id="meetingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 transition duration-150 ease-in-out">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-5xl shadow-lg rounded-md bg-white dark:bg-gray-800 transition duration-150 ease-in-out">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-4 border-b border-gray-200 dark:border-gray-700 dark:border-gray-600">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900 dark:text-white">Add New Meeting</h3>
                <button onclick="closeMeetingModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Tab Navigation -->
            <div class="mt-4">
                <nav class="flex space-x-8" aria-label="Tabs">
                    <button type="button" onclick="switchTab('basic-info')"
                            class="tab-button active whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="basic-info">
                        <i class="fas fa-info-circle mr-2"></i>Basic Info
                    </button>
                    <button type="button" onclick="switchTab('agenda')"
                            class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="agenda">
                        <i class="fas fa-list mr-2"></i>Agenda
                    </button>
                    <button type="button" onclick="switchTab('participants')"
                            class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm"
                            data-tab="participants">
                        <i class="fas fa-users mr-2"></i>Participants
                    </button>
                    <button type="button" onclick="switchTab('minutes')"
                            class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm hidden"
                            data-tab="minutes" id="minutesTab">
                        <i class="fas fa-file-alt mr-2"></i>Minutes
                    </button>
                    <button type="button" onclick="switchTab('status')"
                            class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm hidden"
                            data-tab="status" id="statusTab">
                        <i class="fas fa-flag mr-2"></i>Status
                    </button>
                </nav>
            </div>

            <!-- Modal Body -->
            <form id="meetingForm" class="mt-6">
                <input type="hidden" id="meetingId" name="id">

                <!-- Basic Info Tab -->
                <div id="basic-info-tab" class="tab-content">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Left Column -->
                        <div class="space-y-4">
                            <!-- Title -->
                            <div>
                                <label for="meetingTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Title *</label>
                                <input type="text" id="meetingTitle" name="title" required
                                       class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
                            </div>

                            <!-- Date and Time -->
                            <div>
                                <label for="meetingDateTime" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date & Time *</label>
                                <input type="datetime-local" id="meetingDateTime" name="scheduled_at" required
                                       class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
                            </div>

                            <!-- Duration -->
                            <div>
                                <label for="meetingDuration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Duration (minutes)</label>
                                <input type="number" id="meetingDuration" name="duration_minutes" value="60" min="15" max="480"
                                       class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-4">
                            <!-- Location -->
                            <div>
                                <label for="meetingLocation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Location</label>
                                <input type="text" id="meetingLocation" name="location" placeholder="Conference Room A, Building 1"
                                       class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
                            </div>

                            <!-- Meeting Link -->
                            <div>
                                <label for="meetingLink" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meeting Link</label>
                                <input type="url" id="meetingLink" name="meeting_link" placeholder="https://zoom.us/j/123456789"
                                       class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="meetingDescriptionEditor" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                                <div class="quill-editor-container">
                                    <div id="meetingDescriptionEditor" class="h-[150px]"></div>
                                    <textarea id="meetingDescriptionHidden" name="description" style="display: none;"></textarea>
                                </div>
                                <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Brief description of the meeting purpose and objectives.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agenda Tab -->
                <div id="agenda-tab" class="tab-content hidden">
                    <div>
                        <label for="meetingAgendaEditor" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meeting Agenda</label>
                        <div class="quill-editor-container">
                            <div id="meetingAgendaEditor" class="h-[300px]"></div>
                            <textarea id="meetingAgendaHidden" name="agenda" style="display: none;"></textarea>
                        </div>
                        <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Use the rich text editor to create a detailed agenda for your meeting.</p>
                    </div>
                </div>

                <!-- Participants Tab -->
                <div id="participants-tab" class="tab-content hidden">
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">Meeting Participants</h4>
                            <button type="button" onclick="showAddParticipantModal()"
                                    class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-3 rounded text-sm transition duration-150 ease-in-out">
                                <i class="fas fa-plus mr-1"></i>Add Participant
                            </button>
                        </div>

                        <div id="participantsList" class="space-y-2">
                            <!-- Participants will be dynamically added here -->
                        </div>

                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            <p><i class="fas fa-info-circle mr-1"></i>You will be automatically added as a participant when the meeting is created.</p>
                        </div>
                    </div>
                </div>

                <!-- Minutes Tab (Edit mode only) -->
                <div id="minutes-tab" class="tab-content hidden">
                    <div>
                        <label for="meetingMinutesEditor" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meeting Minutes</label>
                        <div class="quill-editor-container">
                            <div id="meetingMinutesEditor" class="h-[300px]"></div>
                            <textarea id="meetingMinutesHidden" name="minutes" style="display: none;"></textarea>
                        </div>
                        <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Record meeting minutes and key discussion points.</p>
                    </div>
                </div>

                <!-- Status Tab (Edit mode only) -->
                <div id="status-tab" class="tab-content hidden">
                    <div class="space-y-4">
                        <div>
                            <label for="meetingStatus" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Meeting Status</label>
                            <select id="meetingStatus" name="status" class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
                                <option value="scheduled">Scheduled</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>

                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 transition duration-150 ease-in-out">
                            <h5 class="font-medium text-gray-900 dark:text-white mb-2">Status Guidelines:</h5>
                            <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                <li><strong>Scheduled:</strong> Meeting is planned and confirmed</li>
                                <li><strong>In Progress:</strong> Meeting is currently happening</li>
                                <li><strong>Completed:</strong> Meeting has finished successfully</li>
                                <li><strong>Cancelled:</strong> Meeting has been cancelled</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex justify-between">
                    <div class="flex space-x-3">
                        <button type="button" onclick="previousTab()" id="prevButton"
                                class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded hidden transition duration-150 ease-in-out">
                            <i class="fas fa-arrow-left mr-2"></i>Previous
                        </button>
                        <button type="button" onclick="nextTab()" id="nextButton"
                                class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Next<i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>

                    <div class="flex space-x-3">
                        <button type="button" onclick="closeMeetingModal()"
                                class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Cancel
                        </button>
                        <button type="submit" id="submitButton"
                                class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded hidden transition duration-150 ease-in-out">
                            <span id="submitButtonText">Create Meeting</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Participant Selection Modal -->
<div id="participantModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50 transition duration-150 ease-in-out">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800 transition duration-150 ease-in-out">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-4 border-b border-gray-200 dark:border-gray-700 dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add Participants</h3>
                <button onclick="closeParticipantModal()" class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="mt-4">
                <!-- Search Input -->
                <div class="mb-4">
                    <label for="participantSearch" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Users</label>
                    <div class="relative">
                        <input type="text" id="participantSearch" placeholder="Search by name or email..."
                               class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 pl-10 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition duration-150 ease-in-out">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400 dark:text-gray-500"></i>
                        </div>
                    </div>
                </div>

                <!-- Search Results -->
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Available Users</h4>
                    <div id="userSearchResults" class="max-h-60 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md">
                        <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-search text-2xl mb-2"></i>
                            <p>Start typing to search for users...</p>
                        </div>
                    </div>
                </div>

                <!-- Selected Participants Preview -->
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Selected for this session</h4>
                    <div id="selectedParticipantsPreview" class="min-h-[60px] border border-gray-200 dark:border-gray-700 rounded-md p-3">
                        <div class="text-center text-gray-500 dark:text-gray-400">
                            <p class="text-sm">No participants selected yet</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Actions -->
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700 dark:border-gray-600">
                <button type="button" onclick="closeParticipantModal()"
                        class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Cancel
                </button>
                <button type="button" onclick="addSelectedParticipants()"
                        class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Add Participants
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let meetings = <?php echo json_encode($meetings, 15, 512) ?>;
let allMeetings = <?php echo json_encode($meetings, 15, 512) ?>; // Keep original copy for search
let isEditing = false;
let editingId = null;

// Quill editor instances - initialize once and reuse
let descriptionEditor = null;
let agendaEditor = null;
let minutesEditor = null;
let editorsInitialized = false;

// Tab management
let currentTab = 'basic-info';
let tabOrder = ['basic-info', 'agenda', 'participants'];
let editTabOrder = ['basic-info', 'agenda', 'participants', 'minutes', 'status'];
let selectedParticipants = [];

// Search management
let currentSearchQuery = '';
let searchTimeout = null;

document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

function initializePage() {
    renderMeetings();
    initializeEventListeners();
    initializeAllEditors();
}

function initializeAllEditors() {
    if (editorsInitialized) {
        console.log('Editors already initialized');
        return;
    }

    console.log('Initializing all Quill editors...');

    // Initialize description editor
    if (typeof window.initializeQuillEditorWithConfig === 'function') {
        descriptionEditor = window.initializeQuillEditorWithConfig(
            'meetingDescriptionEditor',
            'meetingDescriptionHidden',
            'Enter meeting description...'
        );
        console.log('Description editor initialized:', !!descriptionEditor);
    }

    // Initialize agenda editor
    if (typeof window.initializeQuillEditorWithConfig === 'function') {
        agendaEditor = window.initializeQuillEditorWithConfig(
            'meetingAgendaEditor',
            'meetingAgendaHidden',
            'Enter meeting agenda...'
        );
        console.log('Agenda editor initialized:', !!agendaEditor);
    }

    // Initialize minutes editor
    if (typeof window.initializeQuillEditorWithConfig === 'function') {
        minutesEditor = window.initializeQuillEditorWithConfig(
            'meetingMinutesEditor',
            'meetingMinutesHidden',
            'Enter meeting minutes...'
        );
        console.log('Minutes editor initialized:', !!minutesEditor);
    }

    editorsInitialized = true;
    console.log('All editors initialized');
}

function clearAllEditorContent() {
    console.log('Clearing all editor content...');

    // Clear description editor
    if (descriptionEditor) {
        if (typeof window.clearQuillContent === 'function') {
            window.clearQuillContent(descriptionEditor);
        } else {
            descriptionEditor.setContents([]);
        }
    }

    // Clear agenda editor
    if (agendaEditor) {
        if (typeof window.clearQuillContent === 'function') {
            window.clearQuillContent(agendaEditor);
        } else {
            agendaEditor.setContents([]);
        }
    }

    // Clear minutes editor
    if (minutesEditor) {
        if (typeof window.clearQuillContent === 'function') {
            window.clearQuillContent(minutesEditor);
        } else {
            minutesEditor.setContents([]);
        }
    }

    // Clear hidden inputs
    const hiddenInputs = ['meetingDescriptionHidden', 'meetingAgendaHidden', 'meetingMinutesHidden'];
    hiddenInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.value = '';
        }
    });

    console.log('All editor content cleared');
}

function initializeEventListeners() {
    // Status filter
    document.getElementById('statusFilter').addEventListener('change', function() {
        applyFilters();
    });

    // Form submission
    document.getElementById('meetingForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitMeeting();
    });

    // Participant search
    document.getElementById('participantSearch').addEventListener('input', function(e) {
        const query = e.target.value.trim();
        searchUsers(query);
    });

    // Meeting search
    document.getElementById('meetingSearch').addEventListener('input', function(e) {
        const query = e.target.value.trim();
        currentSearchQuery = query;

        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Debounce search
        searchTimeout = setTimeout(() => {
            performMeetingSearch(query);
        }, 300);

        // Show/hide clear button
        const clearBtn = document.getElementById('clearSearchBtn');
        if (query.length > 0) {
            clearBtn.classList.remove('hidden');
        } else {
            clearBtn.classList.add('hidden');
        }
    });

    // Keyboard navigation for tabs
    document.addEventListener('keydown', function(e) {
        if (document.getElementById('meetingModal').classList.contains('hidden')) {
            return;
        }

        // Tab navigation with arrow keys
        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
            const currentIndex = tabOrder.indexOf(currentTab);
            let newIndex;

            if (e.key === 'ArrowLeft') {
                newIndex = currentIndex > 0 ? currentIndex - 1 : tabOrder.length - 1;
            } else {
                newIndex = currentIndex < tabOrder.length - 1 ? currentIndex + 1 : 0;
            }

            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                switchTab(tabOrder[newIndex]);
            }
        }

        // Close modal with Escape
        if (e.key === 'Escape') {
            closeMeetingModal();
        }
    });
}

function renderMeetings() {
    const container = document.getElementById('meetingsContainer');
    
    if (meetings.length === 0) {
        container.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-calendar-alt text-gray-400 dark:text-gray-500 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No meetings found</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">Get started by creating your first meeting.</p>
                <button onclick="showCreateModal()" class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-plus mr-2"></i>Create Meeting
                </button>
            </div>
        `;
        return;
    }

    const meetingsHtml = meetings.map(meeting => {
        const scheduledDate = new Date(meeting.scheduled_at);
        const isUpcoming = scheduledDate > new Date();
        
        return `
            <div class="border border-gray-200 dark:border-gray-700 dark:border-gray-600 rounded-lg p-4 mb-4 hover:shadow-md transition-shadow">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${meeting.title}</h3>
                            <span class="px-2 py-1 text-xs font-medium rounded-full ${meeting.status_badge_class}">
                                ${meeting.status.charAt(0).toUpperCase() + meeting.status.slice(1)}
                            </span>
                        </div>
                        
                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
                            <i class="fas fa-calendar mr-2"></i>
                            <span>${scheduledDate.toLocaleDateString()} at ${scheduledDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                            <span class="mx-2">•</span>
                            <i class="fas fa-clock mr-1"></i>
                            <span>${meeting.formatted_duration}</span>
                        </div>
                        
                        ${meeting.location ? `
                            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                <span>${meeting.location}</span>
                            </div>
                        ` : ''}
                        
                        <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                            <span><i class="fas fa-users mr-1"></i>${meeting.participant_count} participants</span>
                            <span><i class="fas fa-paperclip mr-1"></i>${meeting.attachment_count} attachments</span>
                            <span><i class="fas fa-tasks mr-1"></i>${meeting.action_item_count} action items</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center gap-2">
                        <button onclick="viewMeeting(${meeting.id})" 
                                class="text-blue-600 hover:text-blue-800 dark:hover:text-blue-300 dark:text-blue-400 dark:hover:text-blue-300">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="editMeeting(${meeting.id})" 
                                class="text-green-600 hover:text-green-800 dark:text-green-200 dark:text-green-400 dark:hover:text-green-300">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteMeeting(${meeting.id})" 
                                class="text-red-600 dark:text-red-400 hover:text-red-800 dark:text-red-200 dark:text-red-400 dark:hover:text-red-300">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = meetingsHtml;
}

function showCreateModal() {
    isEditing = false;
    editingId = null;
    document.getElementById('modalTitle').textContent = 'Add New Meeting';
    document.getElementById('submitButtonText').textContent = 'Create Meeting';
    document.getElementById('meetingForm').reset();
    document.getElementById('meetingId').value = '';

    // Hide editing-only tabs
    document.getElementById('minutesTab').classList.add('hidden');
    document.getElementById('statusTab').classList.add('hidden');

    // Reset tab state
    currentTab = 'basic-info';
    tabOrder = ['basic-info', 'agenda', 'participants'];
    selectedParticipants = [];

    // Clear meeting data
    window.currentMeetingData = null;

    // Clear all editor content instead of destroying
    clearAllEditorContent();

    // Set default date to tomorrow at 9 AM
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(9, 0, 0, 0);
    document.getElementById('meetingDateTime').value = tomorrow.toISOString().slice(0, 16);

    // Switch to first tab
    switchTab('basic-info');

    document.getElementById('meetingModal').classList.remove('hidden');
}

function editMeeting(id) {
    const meeting = meetings.find(m => m.id === id);
    if (!meeting) {
        // If meeting not found in current list, redirect to edit page
        window.location.href = `/meetings/${id}?edit=true`;
        return;
    }

    isEditing = true;
    editingId = id;
    document.getElementById('modalTitle').textContent = 'Edit Meeting';
    document.getElementById('submitButtonText').textContent = 'Update Meeting';

    // Show editing-only tabs
    document.getElementById('minutesTab').classList.remove('hidden');
    document.getElementById('statusTab').classList.remove('hidden');

    // Set tab order for editing
    tabOrder = editTabOrder;
    currentTab = 'basic-info';

    // Fill form fields
    document.getElementById('meetingId').value = meeting.id;
    document.getElementById('meetingTitle').value = meeting.title;
    document.getElementById('meetingDateTime').value = new Date(meeting.scheduled_at).toISOString().slice(0, 16);
    document.getElementById('meetingDuration').value = meeting.duration_minutes;
    document.getElementById('meetingLocation').value = meeting.location || '';
    document.getElementById('meetingLink').value = meeting.meeting_link || '';
    document.getElementById('meetingStatus').value = meeting.status;

    // Load participants
    selectedParticipants = meeting.participants ? meeting.participants.map(p => ({
        id: p.user ? p.user.id : ('contact_' + p.id),
        name: p.user ? p.user.name : (p.contact_name || 'Unknown'),
        email: p.user ? p.user.email : (p.contact_email || ''),
        type: p.user ? 'user' : 'contact',
        status: p.status || 'invited'
    })) : [];



    // Clear all editor content instead of destroying
    clearAllEditorContent();

    // Store meeting data for editor initialization BEFORE switching tabs
    window.currentMeetingData = meeting;

    // Switch to first tab (this will set the content)
    switchTab('basic-info');

    // Render participants after a short delay
    setTimeout(() => {
        renderParticipants();
    }, 150);

    document.getElementById('meetingModal').classList.remove('hidden');
}

// Handle edit mode from URL parameter
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const editId = urlParams.get('edit');

    if (editId) {
        // Remove edit parameter from URL
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);

        // Open edit modal
        editMeeting(parseInt(editId));
    }
});

function closeMeetingModal() {
    document.getElementById('meetingModal').classList.add('hidden');

    // Clear editor content instead of destroying
    clearAllEditorContent();

    // Reset tab state
    currentTab = 'basic-info';
    selectedParticipants = [];

    // Clear meeting data
    window.currentMeetingData = null;
}

function destroyQuillEditors() {
    console.log('Destroying Quill editors...');

    // Clear editor references first
    descriptionEditor = null;
    agendaEditor = null;
    minutesEditor = null;

    // Aggressively clear all editor containers
    const containers = [
        { id: 'meetingDescriptionEditor', height: 'h-[150px]' },
        { id: 'meetingAgendaEditor', height: 'h-[300px]' },
        { id: 'meetingMinutesEditor', height: 'h-[300px]' }
    ];

    containers.forEach(container => {
        const element = document.getElementById(container.id);
        if (element) {
            // Remove any Quill instance reference
            if (element.__quill) {
                try {
                    element.__quill.off('text-change');
                    if (typeof element.__quill.disable === 'function') {
                        element.__quill.disable();
                    }
                } catch (error) {
                    console.warn(`Error disabling Quill for ${container.id}:`, error);
                }
                delete element.__quill;
            }

            // Completely clear the element
            element.innerHTML = '';
            element.className = container.height;

            // Remove all attributes that might be added by Quill
            Array.from(element.attributes).forEach(attr => {
                if (attr.name.startsWith('data-') ||
                    attr.name.startsWith('aria-') ||
                    attr.name === 'contenteditable' ||
                    attr.name === 'spellcheck' ||
                    attr.name === 'role') {
                    element.removeAttribute(attr.name);
                }
            });

            // Remove any inline styles
            element.removeAttribute('style');

            console.log(`Cleared container: ${container.id}`);
        }
    });

    // Clear hidden inputs
    const hiddenInputs = ['meetingDescriptionHidden', 'meetingAgendaHidden', 'meetingMinutesHidden'];
    hiddenInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.value = '';
        }
    });

    console.log('Quill editors destroyed');
}

// Tab switching functions
function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content
    document.getElementById(tabName + '-tab').classList.remove('hidden');

    // Add active class to selected tab button
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

    // Update current tab
    currentTab = tabName;

    // Update navigation buttons
    updateNavigationButtons();

    // Set content if editing and data is available (editors are already initialized)
    if (isEditing && window.currentMeetingData) {
        setTimeout(() => {
            if (tabName === 'basic-info' && descriptionEditor && window.currentMeetingData.description) {
                if (typeof window.setQuillContent === 'function') {
                    window.setQuillContent(descriptionEditor, window.currentMeetingData.description);
                } else {
                    descriptionEditor.root.innerHTML = window.currentMeetingData.description;
                }
                document.getElementById('meetingDescriptionHidden').value = window.currentMeetingData.description;
            } else if (tabName === 'agenda' && agendaEditor && window.currentMeetingData.agenda) {
                if (typeof window.setQuillContent === 'function') {
                    window.setQuillContent(agendaEditor, window.currentMeetingData.agenda);
                } else {
                    agendaEditor.root.innerHTML = window.currentMeetingData.agenda;
                }
                document.getElementById('meetingAgendaHidden').value = window.currentMeetingData.agenda;
            } else if (tabName === 'minutes' && minutesEditor && window.currentMeetingData.minutes) {
                if (typeof window.setQuillContent === 'function') {
                    window.setQuillContent(minutesEditor, window.currentMeetingData.minutes);
                } else {
                    minutesEditor.root.innerHTML = window.currentMeetingData.minutes;
                }
                document.getElementById('meetingMinutesHidden').value = window.currentMeetingData.minutes;
            }
        }, 100);
    }
}

function nextTab() {
    // Validate current tab before proceeding
    if (!validateCurrentTab()) {
        return;
    }

    const currentIndex = tabOrder.indexOf(currentTab);
    if (currentIndex < tabOrder.length - 1) {
        switchTab(tabOrder[currentIndex + 1]);
    }
}

function previousTab() {
    const currentIndex = tabOrder.indexOf(currentTab);
    if (currentIndex > 0) {
        switchTab(tabOrder[currentIndex - 1]);
    }
}

function validateCurrentTab() {
    switch (currentTab) {
        case 'basic-info':
            const title = document.getElementById('meetingTitle').value.trim();
            const dateTime = document.getElementById('meetingDateTime').value;

            if (!title) {
                document.getElementById('meetingTitle').focus();
                showNotification('Please enter a meeting title.', 'error');
                return false;
            }

            if (!dateTime) {
                document.getElementById('meetingDateTime').focus();
                showNotification('Please select a date and time for the meeting.', 'error');
                return false;
            }

            // Check if date is in the past (for new meetings)
            if (!isEditing && new Date(dateTime) < new Date()) {
                document.getElementById('meetingDateTime').focus();
                showNotification('Meeting date cannot be in the past.', 'error');
                return false;
            }

            break;

        case 'participants':
            // No strict validation for participants - it's optional
            break;

        case 'agenda':
            // No strict validation for agenda - it's optional
            break;

        case 'minutes':
            // No strict validation for minutes - it's optional
            break;

        case 'status':
            // No strict validation for status
            break;
    }

    return true;
}

function updateNavigationButtons() {
    const currentIndex = tabOrder.indexOf(currentTab);
    const prevButton = document.getElementById('prevButton');
    const nextButton = document.getElementById('nextButton');
    const submitButton = document.getElementById('submitButton');

    // Show/hide previous button
    if (currentIndex > 0) {
        prevButton.classList.remove('hidden');
    } else {
        prevButton.classList.add('hidden');
    }

    // Show/hide next button and submit button
    if (currentIndex < tabOrder.length - 1) {
        nextButton.classList.remove('hidden');
        submitButton.classList.add('hidden');
    } else {
        nextButton.classList.add('hidden');
        submitButton.classList.remove('hidden');
    }
}



// Individual editor initialization functions
function initializeDescriptionEditor() {
    console.log('Attempting to initialize description editor...');

    // Check if editor is already initialized
    if (descriptionEditor) {
        console.log('Description editor already initialized, returning existing instance');
        return descriptionEditor;
    }

    // Check if container already has a Quill instance
    const container = document.getElementById('meetingDescriptionEditor');
    if (container && container.__quill) {
        console.log('Container already has Quill instance, using existing one');
        descriptionEditor = container.__quill;
        return descriptionEditor;
    }

    if (typeof window.initializeQuillEditorWithConfig === 'function') {
        console.log('Using shared Quill initialization function');
        descriptionEditor = window.initializeQuillEditorWithConfig(
            'meetingDescriptionEditor',
            'meetingDescriptionHidden',
            'Enter meeting description...'
        );
        console.log('Description editor initialized via shared function:', !!descriptionEditor);
        return descriptionEditor;
    } else {
        // Fallback implementation
        console.log('Using fallback Quill initialization for description editor');
        descriptionEditor = createQuillEditorFallback(
            'meetingDescriptionEditor',
            'meetingDescriptionHidden',
            'Enter meeting description...'
        );
        console.log('Description editor initialized via fallback:', !!descriptionEditor);
        return descriptionEditor;
    }
}

function initializeAgendaEditor() {
    // Check if editor is already initialized
    if (agendaEditor) {
        console.log('Agenda editor already initialized');
        return agendaEditor;
    }

    if (typeof window.initializeQuillEditorWithConfig === 'function') {
        agendaEditor = window.initializeQuillEditorWithConfig(
            'meetingAgendaEditor',
            'meetingAgendaHidden',
            'Enter meeting agenda...'
        );
        console.log('Agenda editor initialized:', !!agendaEditor);
        return agendaEditor;
    } else {
        // Fallback implementation
        console.log('Using fallback Quill initialization for agenda editor');
        agendaEditor = createQuillEditorFallback(
            'meetingAgendaEditor',
            'meetingAgendaHidden',
            'Enter meeting agenda...'
        );
        return agendaEditor;
    }
}

function initializeMinutesEditor() {
    // Check if editor is already initialized
    if (minutesEditor) {
        console.log('Minutes editor already initialized');
        return minutesEditor;
    }

    if (typeof window.initializeQuillEditorWithConfig === 'function') {
        minutesEditor = window.initializeQuillEditorWithConfig(
            'meetingMinutesEditor',
            'meetingMinutesHidden',
            'Enter meeting minutes...'
        );
        console.log('Minutes editor initialized:', !!minutesEditor);
        return minutesEditor;
    } else {
        // Fallback implementation
        console.log('Using fallback Quill initialization for minutes editor');
        minutesEditor = createQuillEditorFallback(
            'meetingMinutesEditor',
            'meetingMinutesHidden',
            'Enter meeting minutes...'
        );
        return minutesEditor;
    }
}

// Fallback Quill editor creation function
function createQuillEditorFallback(editorId, hiddenId, placeholder) {
    const editorContainer = document.getElementById(editorId);
    const hiddenInput = document.getElementById(hiddenId);

    if (!editorContainer || !hiddenInput) {
        console.error(`Editor elements not found: ${editorId}, ${hiddenId}`);
        return null;
    }

    // Check if Quill is available
    if (typeof Quill === 'undefined') {
        console.error('Quill is not loaded');
        return null;
    }

    // Custom toolbar configuration for meetings
    const toolbarOptions = [
        ['bold', 'italic', 'underline'],
        [{ 'header': [1, 2, 3, false] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        ['link', 'blockquote'],
        ['clean']
    ];

    try {
        // Initialize Quill
        const quill = new Quill(editorContainer, {
            theme: 'snow',
            placeholder: placeholder,
            modules: {
                toolbar: toolbarOptions,
                history: {
                    delay: 1000,
                    maxStack: 50,
                    userOnly: true
                }
            }
        });

        // Sync content with hidden input
        quill.on('text-change', function() {
            hiddenInput.value = quill.root.innerHTML;
        });

        console.log(`Fallback Quill editor created successfully for ${editorId}`);
        return quill;
    } catch (error) {
        console.error(`Failed to create fallback Quill editor for ${editorId}:`, error);
        return null;
    }
}

// Participant management functions
let tempSelectedParticipants = [];
let allUsers = [];

function showAddParticipantModal() {
    tempSelectedParticipants = [];
    document.getElementById('participantSearch').value = '';
    document.getElementById('userSearchResults').innerHTML = `
        <div class="p-4 text-center text-gray-500 dark:text-gray-400">
            <i class="fas fa-search text-2xl mb-2"></i>
            <p>Start typing to search for users...</p>
        </div>
    `;
    updateSelectedParticipantsPreview();

    // Load all users if not already loaded
    if (allUsers.length === 0) {
        loadAllUsers();
    }

    document.getElementById('participantModal').classList.remove('hidden');
}

function closeParticipantModal() {
    document.getElementById('participantModal').classList.add('hidden');
    tempSelectedParticipants = [];
}

function loadAllUsers() {
    // Load users from the API endpoint
    fetch('/meetings/api/users/search', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            allUsers = data.users;
        }
    })
    .catch(error => {
        console.error('Error loading users:', error);
        // Fallback: create some mock users for demonstration
        allUsers = [
            { id: 1, name: 'John Doe', email: '<EMAIL>' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>' },
            { id: 3, name: 'Mike Johnson', email: '<EMAIL>' },
            { id: 4, name: 'Sarah Wilson', email: '<EMAIL>' },
            { id: 5, name: 'David Brown', email: '<EMAIL>' }
        ];
    });
}

function searchUsers(query) {
    if (!query || query.length < 2) {
        document.getElementById('userSearchResults').innerHTML = `
            <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                <i class="fas fa-search text-2xl mb-2"></i>
                <p>Start typing to search for users...</p>
            </div>
        `;
        return;
    }

    const filteredUsers = allUsers.filter(user =>
        user.name.toLowerCase().includes(query.toLowerCase()) ||
        user.email.toLowerCase().includes(query.toLowerCase())
    );

    if (filteredUsers.length === 0) {
        // Check if the query looks like an email
        const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(query);

        document.getElementById('userSearchResults').innerHTML = `
            <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                <i class="fas fa-user-slash text-2xl mb-2"></i>
                <p>No users or contacts found matching "${query}"</p>
                ${isEmail ? `
                    <div class="mt-4">
                        <button onclick="createNewContact('${query}')"
                                class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded text-sm transition duration-150 ease-in-out">
                            <i class="fas fa-plus mr-1"></i>Create New Contact
                        </button>
                        <p class="text-xs mt-2 text-gray-400 dark:text-gray-500">Create a new contact with email: ${query}</p>
                    </div>
                ` : ''}
            </div>
        `;
        return;
    }

    const resultsHtml = filteredUsers.map(user => {
        const isAlreadyParticipant = selectedParticipants.find(p => p.id === user.id || p.email === user.email);
        const isSelected = tempSelectedParticipants.find(p => p.id === user.id);
        const isDisabled = isAlreadyParticipant ? 'opacity-50 cursor-not-allowed' : '';
        const buttonText = isAlreadyParticipant ? 'Already Added' : (isSelected ? 'Selected' : 'Add');
        const buttonClass = isAlreadyParticipant ? 'bg-gray-400' : (isSelected ? 'bg-green-500 dark:bg-green-600' : 'bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600');

        // Different icons for users vs contacts
        const iconClass = user.type === 'contact' ? 'fas fa-address-book' : 'fas fa-user';
        const bgColor = user.type === 'contact' ? 'bg-green-500 dark:bg-green-600' : 'bg-blue-600 dark:bg-blue-700';

        return `
            <div class="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 dark:border-gray-600 last:border-b-0 ${isDisabled}">
                <div class="flex items-center">
                    <div class="w-8 h-8 ${bgColor} rounded-full flex items-center justify-center mr-3">
                        <i class="${iconClass} text-white text-sm"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900 dark:text-white">${user.name}</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">${user.email}</p>
                        <p class="text-xs text-gray-400 dark:text-gray-500">${user.source || (user.type === 'user' ? 'System User' : 'Contact')}</p>
                    </div>
                </div>
                <button onclick="toggleUserSelection('${user.id}', '${user.name}', '${user.email}', '${user.type || 'user'}')"
                        ${isAlreadyParticipant ? 'disabled' : ''}
                        class="${buttonClass} text-white font-bold py-1 px-3 rounded text-sm">
                    ${buttonText}
                </button>
            </div>
        `;
    }).join('');

    document.getElementById('userSearchResults').innerHTML = resultsHtml;
}

function toggleUserSelection(userId, userName, userEmail, userType = 'user') {
    const existingIndex = tempSelectedParticipants.findIndex(p => p.id === userId);

    if (existingIndex > -1) {
        // Remove from selection
        tempSelectedParticipants.splice(existingIndex, 1);
    } else {
        // Add to selection
        tempSelectedParticipants.push({
            id: userId,
            name: userName,
            email: userEmail,
            type: userType,
            status: 'invited'
        });
    }

    updateSelectedParticipantsPreview();

    // Re-search to update button states
    const query = document.getElementById('participantSearch').value;
    if (query.length >= 2) {
        searchUsers(query);
    }
}

function updateSelectedParticipantsPreview() {
    const container = document.getElementById('selectedParticipantsPreview');

    if (tempSelectedParticipants.length === 0) {
        container.innerHTML = `
            <div class="text-center text-gray-500 dark:text-gray-400">
                <p class="text-sm">No participants selected yet</p>
            </div>
        `;
        return;
    }

    const participantsHtml = tempSelectedParticipants.map(participant => `
        <div class="inline-flex items-center bg-blue-100 dark:bg-blue-900 rounded-full px-3 py-1 text-sm mr-2 mb-2 transition duration-150 ease-in-out">
            <span class="text-blue-800 dark:text-blue-200">${participant.name}</span>
            <button onclick="toggleUserSelection(${participant.id}, '${participant.name}', '${participant.email}')"
                    class="ml-2 text-blue-600 hover:text-blue-800 dark:hover:text-blue-300 dark:text-blue-400 dark:hover:text-blue-300">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');

    container.innerHTML = participantsHtml;
}

function addSelectedParticipants() {
    // Add temp selected participants to the main list
    tempSelectedParticipants.forEach(participant => {
        if (!selectedParticipants.find(p => p.id === participant.id || p.email === participant.email)) {
            selectedParticipants.push(participant);
        }
    });

    renderParticipants();
    closeParticipantModal();

    if (tempSelectedParticipants.length > 0) {
        showNotification(`Added ${tempSelectedParticipants.length} participant(s) to the meeting.`, 'success');
    }
}

function removeParticipant(email) {
    selectedParticipants = selectedParticipants.filter(p => p.email !== email);
    renderParticipants();
}

function createNewContact(email) {
    // Prompt for contact name
    const name = prompt(`Enter the name for contact with email: ${email}`);
    if (!name || name.trim() === '') {
        return;
    }

    // Create a temporary contact participant
    const newContact = {
        id: 'temp_' + Date.now(),
        name: name.trim(),
        email: email,
        type: 'contact',
        status: 'invited',
        isNew: true
    };

    // Add to temp selected participants
    tempSelectedParticipants.push(newContact);
    updateSelectedParticipantsPreview();

    // Clear search and show success message
    document.getElementById('participantSearch').value = '';
    document.getElementById('userSearchResults').innerHTML = `
        <div class="p-4 text-center text-green-600 dark:text-green-400">
            <i class="fas fa-check-circle text-2xl mb-2"></i>
            <p>Contact "${name}" (${email}) added to participants</p>
            <p class="text-xs mt-1 text-gray-500 dark:text-gray-400">This contact will be created when the meeting is saved</p>
        </div>
    `;
}

function renderParticipants() {

    const container = document.getElementById('participantsList');

    if (selectedParticipants.length === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                <i class="fas fa-users text-4xl mb-3"></i>
                <p>No participants added yet.</p>
                <p class="text-sm">Click "Add Participant" to invite people to this meeting.</p>
            </div>
        `;
        return;
    }

    const participantsHtml = selectedParticipants.map(participant => {
        const name = participant.name || 'Unknown';
        const email = participant.email || '';
        const type = participant.type || 'user';
        const bgColor = type === 'contact' ? 'bg-green-500 dark:bg-green-600' : 'bg-blue-600 dark:bg-blue-700';
        const icon = type === 'contact' ? '<i class="fas fa-address-book text-white text-sm"></i>' : `<span class="text-white text-sm font-medium">${name.charAt(0).toUpperCase()}</span>`;

        return `
        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg transition duration-150 ease-in-out">
            <div class="flex items-center">
                <div class="w-8 h-8 ${bgColor} rounded-full flex items-center justify-center mr-3">
                    ${icon}
                </div>
                <div>
                    <p class="font-medium text-gray-900 dark:text-white">${name}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">${email}</p>
                    <p class="text-xs ${type === 'contact' ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}">${type === 'contact' ? 'Contact' : 'System User'}</p>
                </div>
            </div>
            <div class="flex items-center gap-2">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 dark:bg-gray-600 dark:text-gray-200 transition duration-150 ease-in-out">
                    ${participant.status || 'invited'}
                </span>
                <button onclick="removeParticipant('${email}')"
                        class="text-red-600 dark:text-red-400 hover:text-red-800 dark:text-red-200 dark:text-red-400 dark:hover:text-red-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        `;
    }).join('');

    container.innerHTML = participantsHtml;
}



function submitMeeting() {
    // Validate required fields
    const title = document.getElementById('meetingTitle').value.trim();
    const dateTime = document.getElementById('meetingDateTime').value;

    if (!title) {
        switchTab('basic-info');
        document.getElementById('meetingTitle').focus();
        showNotification('Please enter a meeting title.', 'error');
        return;
    }

    if (!dateTime) {
        switchTab('basic-info');
        document.getElementById('meetingDateTime').focus();
        showNotification('Please select a date and time for the meeting.', 'error');
        return;
    }

    // Sync editor content to hidden fields
    if (descriptionEditor) {
        document.getElementById('meetingDescriptionHidden').value = descriptionEditor.root.innerHTML;
    }
    if (agendaEditor) {
        document.getElementById('meetingAgendaHidden').value = agendaEditor.root.innerHTML;
    }
    if (minutesEditor) {
        document.getElementById('meetingMinutesHidden').value = minutesEditor.root.innerHTML;
    }

    const formData = new FormData(document.getElementById('meetingForm'));
    const data = Object.fromEntries(formData.entries());

    // Add participants data
    if (selectedParticipants.length > 0) {
        data.participants = selectedParticipants.map(p => {
            if (p.isNew && p.type === 'contact') {
                // New contact to be created
                return {
                    type: 'new_contact',
                    name: p.name,
                    email: p.email
                };
            } else {
                // Existing user or contact
                return p.id || p.email;
            }
        });
    }

    const url = isEditing ? `/meetings/${editingId}` : '/meetings';

    if (isEditing) {
        data._method = 'PUT';
    }

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('Server returned non-JSON response. Please check server logs.');
        }

        return response.json();
    })
    .then(data => {
        if (data.success) {
            closeMeetingModal();

            if (isEditing) {
                // Update existing meeting in both arrays
                const index = meetings.findIndex(m => m.id === editingId);
                const allIndex = allMeetings.findIndex(m => m.id === editingId);
                if (index !== -1) {
                    meetings[index] = data.meeting;
                }
                if (allIndex !== -1) {
                    allMeetings[allIndex] = data.meeting;
                }
            } else {
                // Add new meeting to both arrays
                meetings.unshift(data.meeting);
                allMeetings.unshift(data.meeting);
            }

            renderMeetings();
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        let errorMessage = 'An error occurred while saving the meeting.';

        if (error.message.includes('non-JSON response')) {
            errorMessage = 'Server error: Please check your data and try again. If the problem persists, contact support.';
        } else if (error.message.includes('HTTP error')) {
            errorMessage = `Server error (${error.message}). Please try again.`;
        }

        showNotification(errorMessage, 'error');
    });
}

function deleteMeeting(id) {
    if (!confirm('Are you sure you want to delete this meeting? This action cannot be undone.')) {
        return;
    }

    fetch(`/meetings/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            meetings = meetings.filter(m => m.id !== id);
            allMeetings = allMeetings.filter(m => m.id !== id);
            renderMeetings();
            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while deleting the meeting.', 'error');
    });
}

function viewMeeting(id) {
    window.location.href = `/meetings/${id}`;
}

function applyFilters() {
    const status = document.getElementById('statusFilter').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;

    const params = new URLSearchParams();
    if (status !== 'all') params.append('status', status);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    fetch(`/meetings/api/list?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allMeetings = data.meetings; // Update the master list

                // Apply search if there's an active search query
                if (currentSearchQuery && currentSearchQuery.length >= 2) {
                    performMeetingSearch(currentSearchQuery);
                } else {
                    meetings = data.meetings;
                    renderMeetings();
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred while filtering meetings.', 'error');
        });
}

// Meeting search functions
function performMeetingSearch(query) {
    if (!query || query.length < 2) {
        // Show all meetings if query is too short
        meetings = [...allMeetings];
        renderMeetings();
        return;
    }

    const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);

    const filteredMeetings = allMeetings.filter(meeting => {
        // Create searchable content
        const searchableContent = [
            meeting.title || '',
            meeting.description || '',
            meeting.agenda || '',
            meeting.minutes || '',
            meeting.location || '',
            ...(meeting.participants || []).map(p => {
                if (p.user) {
                    return `${p.user.name || ''} ${p.user.email || ''}`;
                } else {
                    return `${p.contact_name || ''} ${p.contact_email || ''}`;
                }
            }),
            ...(meeting.action_items || []).map(item => `${item.title} ${item.description || ''}`),
        ].join(' ').toLowerCase();

        // Remove HTML tags from content
        const cleanContent = searchableContent.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ');

        // Check if all search terms are found
        return searchTerms.every(term => cleanContent.includes(term));
    });

    meetings = filteredMeetings;
    renderMeetings();

    // Show search results info
    if (filteredMeetings.length === 0) {
        showSearchResultsInfo(query, 0);
    } else {
        showSearchResultsInfo(query, filteredMeetings.length);
    }
}

function clearSearch() {
    document.getElementById('meetingSearch').value = '';
    document.getElementById('clearSearchBtn').classList.add('hidden');
    currentSearchQuery = '';
    meetings = [...allMeetings];
    renderMeetings();
    hideSearchResultsInfo();
}

function showSearchResultsInfo(query, count) {
    // Remove existing search info
    hideSearchResultsInfo();

    const searchInfo = document.createElement('div');
    searchInfo.id = 'searchResultsInfo';
    searchInfo.className = 'bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg p-3 mb-4';
    searchInfo.innerHTML = `
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <i class="fas fa-search text-blue-600 dark:text-blue-400 mr-2"></i>
                <span class="text-blue-800 dark:text-blue-200">
                    ${count === 0 ? 'No meetings found' : `Found ${count} meeting${count !== 1 ? 's' : ''}`}
                    for "${query}"
                </span>
            </div>
            <button onclick="clearSearch()" class="text-blue-600 hover:text-blue-800 dark:hover:text-blue-300 dark:text-blue-400 dark:hover:text-blue-300">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    const container = document.getElementById('meetingsContainer');
    container.parentNode.insertBefore(searchInfo, container);
}

function hideSearchResultsInfo() {
    const searchInfo = document.getElementById('searchResultsInfo');
    if (searchInfo) {
        searchInfo.remove();
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 dark:bg-green-600 text-white' : 'bg-red-500 dark:bg-red-600 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>

<!-- Include Quill CSS -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<!-- Include Quill JS -->
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

<!-- Tab Styles and Quill Editor Styles -->
<style>
/* Quill Editor Container Styling */
.quill-editor-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    height: auto;
}

.quill-editor-container:focus-within {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.quill-editor-container:hover {
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1) !important;
}

/* Quill Editor Toolbar Styling */
.ql-toolbar {
    border: 1px solid #e5e7eb !important;
    border-bottom: 1px solid #d1d5db !important;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    padding: 16px 20px !important;
    border-radius: 12px 12px 0 0 !important;
    position: relative;
    overflow: hidden;
}

.ql-toolbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ql-toolbar:hover::before {
    opacity: 1;
}

.dark .ql-toolbar {
    border-color: #374151 !important;
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
}

.dark .ql-toolbar::before {
    background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.4), transparent);
}

/* Quill Editor Button Styling */
.ql-toolbar .ql-formats {
    margin-right: 15px;
}

.ql-toolbar button {
    width: 32px !important;
    height: 32px !important;
    border-radius: 8px !important;
    margin: 0 2px !important;
    border: 1px solid transparent !important;
    background: transparent !important;
    color: #6b7280 !important;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.ql-toolbar button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.1));
    opacity: 0;
    transition: opacity 0.2s ease;
    border-radius: 6px;
}

.ql-toolbar button:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;
    color: #374151 !important;
    border-color: #e2e8f0 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.ql-toolbar button:hover::before {
    opacity: 1;
}

.ql-toolbar button.ql-active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
    color: #ffffff !important;
    border-color: #2563eb !important;
}

.dark .ql-toolbar button {
    color: #9ca3af !important;
}

.dark .ql-toolbar button:hover {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
    color: #f3f4f6 !important;
    border-color: #4b5563 !important;
}

.dark .ql-toolbar button.ql-active {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
    color: #ffffff !important;
    border-color: rgba(96, 165, 250, 0.3) !important;
}

/* Quill Editor Content Styling */
.ql-editor {
    border: 1px solid #e5e7eb !important;
    border-top: none !important;
    border-radius: 0 0 12px 12px !important;
    min-height: 150px !important;
    padding: 16px !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    color: #374151 !important;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.ql-editor::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ql-editor:focus::before {
    opacity: 1;
}

.dark .ql-editor {
    border-color: #374151 !important;
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
    color: #f9fafb !important;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.dark .ql-editor::before {
    background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.3), transparent);
}

.ql-editor:focus {
    border-color: #3b82f6 !important;
    box-shadow:
        inset 0 1px 2px rgba(0, 0, 0, 0.05),
        0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
}

.dark .ql-editor:focus {
    border-color: #3b82f6 !important;
    box-shadow:
        inset 0 1px 2px rgba(0, 0, 0, 0.2),
        0 0 0 3px rgba(59, 130, 246, 0.2) !important;
    background: linear-gradient(135deg, #1f2937 0%, #0f172a 100%) !important;
}

/* Tab Styles */
.tab-button {
    border-bottom-color: transparent;
    color: #6B7280;
    transition: all 0.2s ease-in-out;
}

.tab-button:hover {
    color: #374151;
    border-bottom-color: #D1D5DB;
}

.tab-button.active {
    color: #3B82F6;
    border-bottom-color: #3B82F6;
}

.dark .tab-button {
    color: #9CA3AF;
}

.dark .tab-button:hover {
    color: #D1D5DB;
    border-bottom-color: #4B5563;
}

.dark .tab-button.active {
    color: #60A5FA;
    border-bottom-color: #60A5FA;
}

.tab-content {
    min-height: 300px;
}

@media (max-width: 768px) {
    .tab-button {
        font-size: 0.75rem;
        padding: 0.5rem 0.25rem;
        flex: 1;
        text-align: center;
    }

    .tab-button i {
        display: none;
    }

    nav[aria-label="Tabs"] {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    nav[aria-label="Tabs"]::-webkit-scrollbar {
        display: none;
    }

    .tab-content {
        min-height: 250px;
    }
}

@media (max-width: 640px) {
    .tab-button {
        font-size: 0.7rem;
        padding: 0.4rem 0.2rem;
    }

    /* Stack form fields on mobile */
    .grid.md\\:grid-cols-2 {
        grid-template-columns: 1fr;
    }

    /* Adjust modal size on mobile */
    .max-w-5xl {
        width: 95%;
        max-width: none;
        margin: 0.5rem;
    }
}
</style>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/meetings/Views/index.blade.php ENDPATH**/ ?>