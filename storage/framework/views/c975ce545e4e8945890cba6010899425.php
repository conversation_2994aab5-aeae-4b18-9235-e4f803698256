<?php $__env->startSection('title', 'Add Business Relationship'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Add Business Relationship</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Create a new relationship for <?php echo e($business->name); ?></p>
            </div>
            <a href="<?php echo e(route('business.relationships.index', $business)); ?>"
               class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Back to Relationships
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <form method="POST" action="<?php echo e(route('business.relationships.store', $business)); ?>" class="p-6">
                <?php echo csrf_field(); ?>

                <!-- Business Search Section -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Select Business</h3>
                    
                    <!-- Search Input -->
                    <div class="mb-4">
                        <label for="business_search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Search for Business
                        </label>
                        <div class="relative">
                            <input type="text" 
                                   id="business_search" 
                                   placeholder="Type business name, brand name, or city..."
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Search Results -->
                    <div id="search_results" class="hidden mb-4">
                        <div class="border border-gray-300 dark:border-gray-600 rounded-md max-h-60 overflow-y-auto">
                            <!-- Results will be populated here -->
                        </div>
                    </div>

                    <!-- Selected Business -->
                    <div id="selected_business" class="hidden mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Selected Business
                        </label>
                        <div class="p-3 border border-green-300 dark:border-green-600 rounded-md bg-green-50 dark:bg-green-900/20">
                            <div class="flex items-center justify-between">
                                <div id="selected_business_info">
                                    <!-- Selected business info will be shown here -->
                                </div>
                                <button type="button" onclick="clearSelection()" class="text-red-600 dark:text-red-400 hover:text-red-800">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden input for selected business ID -->
                    <input type="hidden" name="related_business_id" id="related_business_id" value="<?php echo e(old('related_business_id')); ?>">
                    <?php $__errorArgs = ['related_business_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Relationship Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Relationship Type -->
                    <div>
                        <label for="relationship_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Relationship Type <span class="text-red-500">*</span>
                        </label>
                        <select name="relationship_type" id="relationship_type" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            <option value="">Select relationship type</option>
                            <?php $__currentLoopData = $relationshipTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($value); ?>" <?php echo e(old('relationship_type') === $value ? 'selected' : ''); ?>>
                                    <?php echo e($label); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['relationship_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="is_active" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Status
                        </label>
                        <select name="is_active" id="is_active"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            <option value="1" <?php echo e(old('is_active', '1') === '1' ? 'selected' : ''); ?>>Active</option>
                            <option value="0" <?php echo e(old('is_active') === '0' ? 'selected' : ''); ?>>Inactive</option>
                        </select>
                        <?php $__errorArgs = ['is_active'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Date Range -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Start Date -->
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Start Date
                        </label>
                        <input type="date" name="start_date" id="start_date" value="<?php echo e(old('start_date')); ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- End Date -->
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            End Date
                        </label>
                        <input type="date" name="end_date" id="end_date" value="<?php echo e(old('end_date')); ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Description -->
                <div class="mb-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Description
                    </label>
                    <textarea name="description" id="description" rows="3" placeholder="Optional description of the relationship..."
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"><?php echo e(old('description')); ?></textarea>
                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-3">
                    <a href="<?php echo e(route('business.relationships.index', $business)); ?>"
                       class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit"
                            class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Create Relationship
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('business_search');
    const searchResults = document.getElementById('search_results');
    const selectedBusiness = document.getElementById('selected_business');
    const selectedBusinessInfo = document.getElementById('selected_business_info');
    const relatedBusinessIdInput = document.getElementById('related_business_id');
    
    let searchTimeout;

    // Handle search input
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        
        clearTimeout(searchTimeout);
        
        if (query.length < 2) {
            searchResults.classList.add('hidden');
            return;
        }

        searchTimeout = setTimeout(() => {
            searchBusinesses(query);
        }, 300);
    });

    function searchBusinesses(query) {
        fetch(`<?php echo e(route('business.relationships.search', $business)); ?>?search=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data.businesses);
            })
            .catch(error => {
                console.error('Search error:', error);
            });
    }

    function displaySearchResults(businesses) {
        if (businesses.length === 0) {
            searchResults.innerHTML = '<div class="p-3 text-gray-500 dark:text-gray-400">No businesses found</div>';
            searchResults.classList.remove('hidden');
            return;
        }

        const resultsHtml = businesses.map(business => `
            <div class="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0" 
                 onclick="selectBusiness(${business.id}, '${business.name}', '${business.brand_name || ''}', '${business.city || ''}', '${business.status}')">
                <div class="font-medium text-gray-900 dark:text-white">${business.display_name}</div>
                ${business.city ? `<div class="text-sm text-gray-500 dark:text-gray-400">${business.city}</div>` : ''}
                <div class="text-xs text-gray-400 dark:text-gray-500">Status: ${business.status}</div>
            </div>
        `).join('');

        searchResults.innerHTML = resultsHtml;
        searchResults.classList.remove('hidden');
    }

    window.selectBusiness = function(id, name, brandName, city, status) {
        relatedBusinessIdInput.value = id;
        
        const displayName = brandName || name;
        const locationText = city ? ` - ${city}` : '';
        
        selectedBusinessInfo.innerHTML = `
            <div>
                <div class="font-medium text-gray-900 dark:text-white">${displayName}</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Status: ${status}${locationText}</div>
            </div>
        `;
        
        selectedBusiness.classList.remove('hidden');
        searchResults.classList.add('hidden');
        searchInput.value = '';
    };

    window.clearSelection = function() {
        relatedBusinessIdInput.value = '';
        selectedBusiness.classList.add('hidden');
        searchInput.value = '';
    };

    // Show selected business if there's an old value
    <?php if(old('related_business_id')): ?>
        // You might want to fetch and display the selected business info here
        selectedBusiness.classList.remove('hidden');
    <?php endif; ?>
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/relationships/create.blade.php ENDPATH**/ ?>