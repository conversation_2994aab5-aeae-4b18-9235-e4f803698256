<?php $__env->startSection('title', 'All Contacts'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-7xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">All Contacts</h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Manage contacts across all businesses</p>
            </div>
            <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                <a href="<?php echo e(route('business.import.index')); ?>"
                   class="bg-green-500 dark:bg-green-600 hover:bg-green-700 dark:hover:bg-green-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    <i class="fas fa-upload mr-2"></i>
                    Import Data
                </a>
            <?php endif; ?>
        </div>

        <!-- Search and Filter Form -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6 transition duration-150 ease-in-out">
            <form method="GET" action="<?php echo e(route('contacts.index')); ?>" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-64">
                    <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                           placeholder="Search contacts..." 
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-gray-700 dark:text-white">
                </div>
                <div>
                    <select name="business_id" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-gray-700 dark:text-white">
                        <option value="">All Businesses</option>
                        <?php $__currentLoopData = $businesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $business): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($business->id); ?>" <?php echo e(request('business_id') == $business->id ? 'selected' : ''); ?>>
                                <?php echo e($business->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div>
                    <select name="is_primary" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-gray-700 dark:text-white">
                        <option value="">All Contacts</option>
                        <option value="1" <?php echo e(request('is_primary') === '1' ? 'selected' : ''); ?>>Primary Only</option>
                        <option value="0" <?php echo e(request('is_primary') === '0' ? 'selected' : ''); ?>>Non-Primary Only</option>
                    </select>
                </div>
                <button type="submit" class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Search
                </button>
                <?php if(request()->hasAny(['search', 'business_id', 'is_primary'])): ?>
                    <a href="<?php echo e(route('contacts.index')); ?>" class="bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500 text-gray-800 dark:text-gray-200 font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Clear
                    </a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Bulk Actions -->
        <?php if($contacts->count() > 0 && auth()->user()->hasPermission('manage_businesses')): ?>
            <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-4 p-4 transition duration-150 ease-in-out">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="selectAll"
                                   class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <label for="selectAll" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Select All</label>
                        </div>
                        <span id="selectedCount" class="text-sm text-gray-500 dark:text-gray-400">0 selected</span>
                    </div>
                    <button type="button" id="bulkDeleteBtn" onclick="confirmBulkDelete()"
                            class="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled>
                        Delete Selected
                    </button>
                </div>
            </div>
        <?php endif; ?>



        <!-- Contacts Table -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <?php if($contacts->count() > 0): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                            <tr>
                                <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-12">
                                        <span class="sr-only">Select</span>
                                    </th>
                                <?php endif; ?>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Contact</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Business</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Position</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Contact Info</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                            <?php $__currentLoopData = $contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                                    <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" name="contact_ids[]" value="<?php echo e($contact->id); ?>"
                                                   class="contact-checkbox rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                                   <?php echo e($contact->is_primary ? 'disabled title="Cannot delete primary contact"' : ''); ?>>
                                        </td>
                                    <?php endif; ?>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="h-10 w-10 flex-shrink-0">
                                                <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center transition duration-150 ease-in-out">
                                                    <i class="fas fa-user text-gray-600 dark:text-gray-400"></i>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($contact->name); ?></div>
                                                <?php if($contact->department): ?>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($contact->department); ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <?php if($contact->business): ?>
                                                <img class="h-8 w-8 rounded-full mr-3" src="<?php echo e($contact->business->logo_url); ?>" alt="<?php echo e($contact->business->name); ?>">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($contact->business->name); ?></div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($contact->business->status_label); ?></div>
                                                </div>
                                            <?php else: ?>
                                                <div class="h-8 w-8 rounded-full mr-3 bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                                    <i class="fas fa-building text-gray-600 dark:text-gray-400 text-xs"></i>
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white">No Business</div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">N/A</div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white"><?php echo e($contact->position ?: 'N/A'); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900 dark:text-white"><?php echo e($contact->email ?: 'N/A'); ?></div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">
                                            <?php echo e($contact->formatted_phone ?: ($contact->phone2 ? $contact->formatted_phone2 : 'N/A')); ?>

                                            <?php if($contact->phone && $contact->phone2): ?>
                                                <br><span class="text-xs">+ <?php echo e($contact->formatted_phone2); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if($contact->is_primary): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 transition duration-150 ease-in-out">
                                                Primary
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 transition duration-150 ease-in-out">
                                                Contact
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="<?php echo e(route('contacts.show', $contact)); ?>"
                                               class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300" title="View Contact">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if($contact->business): ?>
                                                <a href="<?php echo e(route('business.show', $contact->business)); ?>"
                                                   class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300" title="View Business">
                                                    <i class="fas fa-building"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($contact->email): ?>
                                                <a href="mailto:<?php echo e($contact->email); ?>"
                                                   class="text-green-600 dark:text-green-400 hover:text-green-900" title="Send Email">
                                                    <i class="fas fa-envelope"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($contact->phone): ?>
                                                <a href="tel:<?php echo e($contact->phone); ?>"
                                                   class="text-purple-600 dark:text-purple-400 hover:text-purple-900" title="Call <?php echo e($contact->formatted_phone); ?>">
                                                    <i class="fas fa-phone"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($contact->phone2): ?>
                                                <a href="tel:<?php echo e($contact->phone2); ?>"
                                                   class="text-purple-600 dark:text-purple-400 hover:text-purple-900" title="Call <?php echo e($contact->formatted_phone2); ?>">
                                                    <i class="fas fa-mobile-alt"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                                <a href="<?php echo e(route('contacts.edit', $contact)); ?>"
                                                   class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Edit Contact">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form method="POST" action="<?php echo e(route('contacts.destroy', $contact)); ?>" class="inline-block ml-2"
                                                      onsubmit="return confirm('Are you sure you want to delete this contact?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit"
                                                            class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                                                            title="Delete Contact">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    <?php echo e($contacts->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-12">
                    <i class="fas fa-address-book text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                    <div class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-lg">No contacts found.</div>
                    <p class="text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-sm mt-2">
                        <?php if(request()->hasAny(['search', 'business_id', 'is_primary'])): ?>
                            Try adjusting your search criteria.
                        <?php else: ?>
                            Contacts will appear here when businesses add them.
                        <?php endif; ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>



        <!-- Contact Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-5 gap-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-address-book text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Contacts</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($contacts->total()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-star text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Primary Contacts</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($contacts->where('is_primary', true)->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-building text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Businesses</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($contacts->pluck('business_id')->unique()->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-envelope text-orange-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">With Email</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($contacts->whereNotNull('email')->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-phone text-indigo-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">With Phone</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($contacts->filter(function($contact) { return $contact->phone || $contact->phone2; })->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if($contacts->count() > 0 && auth()->user()->hasPermission('manage_businesses')): ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const contactCheckboxes = document.querySelectorAll('.contact-checkbox:not([disabled])');
    const selectedCountElement = document.getElementById('selectedCount');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');

    // Update selected count and button state
    function updateBulkDeleteState() {
        const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked:not([disabled])');
        const count = checkedBoxes.length;

        selectedCountElement.textContent = `${count} selected`;
        bulkDeleteBtn.disabled = count === 0;

        // Update select all checkbox state
        if (selectAllCheckbox) {
            if (count === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (count === contactCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllCheckbox.checked = false;
            }
        }
    }

    // Handle select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            contactCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkDeleteState();
        });
    }

    // Handle individual checkbox changes
    contactCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkDeleteState);
    });

    // Initialize state
    updateBulkDeleteState();
});

function confirmBulkDelete() {
    const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked:not([disabled])');
    const count = checkedBoxes.length;

    if (count === 0) {
        alert('Please select contacts to delete.');
        return;
    }

    if (confirm(`Are you sure you want to delete ${count} contact(s)? This action cannot be undone.`)) {
        // Create a form dynamically with the selected contact IDs
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("contacts.bulk-destroy")); ?>';

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // Add method override for DELETE
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // Add selected contact IDs
        checkedBoxes.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'contact_ids[]';
            input.value = checkbox.value;
            form.appendChild(input);
        });

        // Submit the form
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/contacts/global-index.blade.php ENDPATH**/ ?>