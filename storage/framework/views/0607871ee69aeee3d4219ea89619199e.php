<?php $__env->startSection('title', 'Business Contacts'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Contacts: <?php echo e($business->name); ?></h1>
                <p class="text-gray-600 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mt-1">Manage contacts for this business</p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('business.show', $business)); ?>"
                   class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Business
                </a>
                <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                    <a href="<?php echo e(route('business.contacts.create', $business)); ?>"
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Add Contact
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Bulk Actions -->
        <?php if($contacts->count() > 0 && auth()->user()->hasPermission('manage_businesses')): ?>
            <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-4 p-4 transition duration-150 ease-in-out">
                <form id="bulkDeleteForm" method="POST" action="<?php echo e(route('business.contacts.bulk-destroy', $business)); ?>">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="selectAll"
                                       class="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <label for="selectAll" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Select All</label>
                            </div>
                            <span id="selectedCount" class="text-sm text-gray-500 dark:text-gray-400">0 selected</span>
                        </div>
                        <button type="button" id="bulkDeleteBtn" onclick="confirmBulkDelete()"
                                class="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled>
                            Delete Selected
                        </button>
                    </div>
                </form>
            </div>
        <?php endif; ?>

        <!-- Contacts List -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <?php if($contacts->count() > 0): ?>
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                        <tr>
                            <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-12">
                                    <span class="sr-only">Select</span>
                                </th>
                            <?php endif; ?>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Position</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Contact Info</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                        <?php $__currentLoopData = $contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" name="contact_ids[]" value="<?php echo e($contact->id); ?>"
                                               class="contact-checkbox rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                               <?php echo e($contact->is_primary ? 'disabled title="Cannot delete primary contact"' : ''); ?>>
                                    </td>
                                <?php endif; ?>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center transition duration-150 ease-in-out">
                                                <i class="fas fa-user text-gray-600 dark:text-gray-400"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?php echo e($contact->name); ?>

                                                <?php if($contact->is_primary): ?>
                                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition duration-150 ease-in-out">
                                                        Primary
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <?php if($contact->department): ?>
                                                <div class="text-sm text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500"><?php echo e($contact->department); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white"><?php echo e($contact->position ?? 'N/A'); ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white">
                                        <?php if($contact->email): ?>
                                            <div class="flex items-center">
                                                <i class="fas fa-envelope text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-2"></i>
                                                <a href="mailto:<?php echo e($contact->email); ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300">
                                                    <?php echo e($contact->email); ?>

                                                </a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($contact->phone): ?>
                                            <div class="flex items-center mt-1">
                                                <i class="fas fa-phone text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-2"></i>
                                                <a href="tel:<?php echo e($contact->phone); ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300">
                                                    <?php echo e($contact->formatted_phone); ?>

                                                </a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($contact->phone2): ?>
                                            <div class="flex items-center mt-1">
                                                <i class="fas fa-mobile-alt text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 mr-2"></i>
                                                <a href="tel:<?php echo e($contact->phone2); ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:text-blue-200 dark:hover:text-blue-300">
                                                    <?php echo e($contact->formatted_phone2); ?>

                                                </a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if(!$contact->email && !$contact->phone && !$contact->phone2): ?>
                                            <span class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500">No contact info</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($contact->isComplete() ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'); ?> transition duration-150 ease-in-out">
                                        <?php echo e($contact->isComplete() ? 'Complete' : 'Incomplete'); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="<?php echo e(route('business.contacts.show', [$business, $contact])); ?>" 
                                           class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">View</a>
                                        <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                                            <a href="<?php echo e(route('business.contacts.edit', [$business, $contact])); ?>" 
                                               class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300">Edit</a>
                                            <?php if(!$contact->is_primary): ?>
                                                <form method="POST" action="<?php echo e(route('business.contacts.set-primary', [$business, $contact])); ?>" class="inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('PATCH'); ?>
                                                    <button type="submit" class="text-green-600 dark:text-green-400 hover:text-green-900">
                                                        Set Primary
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                            <form method="POST" action="<?php echo e(route('business.contacts.destroy', [$business, $contact])); ?>" 
                                                  onsubmit="return confirm('Are you sure you want to delete this contact?')" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">Delete</button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 text-lg">No contacts found for this business.</div>
                    <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                        <a href="<?php echo e(route('business.contacts.create', $business)); ?>" 
                           class="mt-4 inline-block bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Add First Contact
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Quick Stats -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-users text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">Total Contacts</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($contacts->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-envelope text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">With Email</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($contacts->whereNotNull('email')->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-phone text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 dark:text-gray-500 dark:text-gray-400 dark:text-gray-500 truncate">With Phone</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($contacts->filter(function($contact) { return $contact->phone || $contact->phone2; })->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if($contacts->count() > 0 && auth()->user()->hasPermission('manage_businesses')): ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const contactCheckboxes = document.querySelectorAll('.contact-checkbox:not([disabled])');
    const selectedCountElement = document.getElementById('selectedCount');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');

    // Update selected count and button state
    function updateBulkDeleteState() {
        const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked:not([disabled])');
        const count = checkedBoxes.length;

        selectedCountElement.textContent = `${count} selected`;
        bulkDeleteBtn.disabled = count === 0;

        // Update select all checkbox state
        if (selectAllCheckbox) {
            if (count === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (count === contactCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllCheckbox.checked = false;
            }
        }
    }

    // Handle select all functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            contactCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkDeleteState();
        });
    }

    // Handle individual checkbox changes
    contactCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkDeleteState);
    });

    // Initialize state
    updateBulkDeleteState();
});

function confirmBulkDelete() {
    const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked:not([disabled])');
    const count = checkedBoxes.length;

    if (count === 0) {
        alert('Please select contacts to delete.');
        return;
    }

    if (confirm(`Are you sure you want to delete ${count} contact(s)? This action cannot be undone.`)) {
        document.getElementById('bulkDeleteForm').submit();
    }
}
</script>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/contacts/index.blade.php ENDPATH**/ ?>