<?php $__env->startSection('title', 'Business Relationships'); ?>

<?php $__env->startSection('content'); ?>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-6xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Business Relationships: <?php echo e($business->name); ?></h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Manage business partnerships and relationships</p>
            </div>
            <div class="flex space-x-3">
                <a href="<?php echo e(route('business.show', $business)); ?>"
                   class="bg-gray-500 dark:bg-gray-600 hover:bg-gray-600 dark:hover:bg-gray-500 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                    Back to Business
                </a>
                <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                    <a href="<?php echo e(route('business.relationships.create', $business)); ?>" 
                       class="bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                        Add Relationship
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Relationships List -->
        <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg transition duration-150 ease-in-out">
            <?php if($relationships->count() > 0): ?>
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700 transition duration-150 ease-in-out">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Business</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Relationship Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Duration</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 transition duration-150 ease-in-out">
                        <?php $__currentLoopData = $relationships; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relationship): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $relatedBusiness = $relationship->business_id === $business->id 
                                    ? $relationship->relatedBusiness 
                                    : $relationship->business;
                                $isOwner = $relationship->business_id === $business->id;
                            ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center transition duration-150 ease-in-out">
                                                <i class="fas fa-building text-gray-600 dark:text-gray-400"></i>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                <a href="<?php echo e(route('business.show', $relatedBusiness)); ?>" class="hover:text-blue-600 dark:hover:text-blue-400">
                                                    <?php echo e($relatedBusiness->name); ?>

                                                </a>
                                            </div>
                                            <?php if($relatedBusiness->brand_name && $relatedBusiness->brand_name !== $relatedBusiness->name): ?>
                                                <div class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($relatedBusiness->brand_name); ?></div>
                                            <?php endif; ?>
                                            <?php if($relatedBusiness->city): ?>
                                                <div class="text-xs text-gray-400 dark:text-gray-500"><?php echo e($relatedBusiness->city); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                        <?php echo e($relationship->relationship_type_display); ?>

                                    </span>
                                    <?php if(!$isOwner): ?>
                                        <span class="ml-1 text-xs text-gray-500 dark:text-gray-400">(Inverse)</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($relationship->isCurrentlyActive() ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'); ?>">
                                        <?php echo e($relationship->isCurrentlyActive() ? 'Active' : 'Inactive'); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    <?php if($relationship->start_date): ?>
                                        <?php echo e($relationship->start_date->format('M j, Y')); ?>

                                    <?php else: ?>
                                        N/A
                                    <?php endif; ?>
                                    <?php if($relationship->end_date): ?>
                                        - <?php echo e($relationship->end_date->format('M j, Y')); ?>

                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <?php if($isOwner && auth()->user()->hasPermission('manage_businesses')): ?>
                                            <a href="<?php echo e(route('business.relationships.edit', [$business, $relationship])); ?>" 
                                               class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300">Edit</a>
                                            <form method="POST" action="<?php echo e(route('business.relationships.destroy', [$business, $relationship])); ?>" 
                                                  onsubmit="return confirm('Are you sure you want to delete this relationship?')" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300">Delete</button>
                                            </form>
                                        <?php else: ?>
                                            <span class="text-gray-400 dark:text-gray-500">View Only</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="text-gray-500 dark:text-gray-400 text-lg">No business relationships found.</div>
                    <?php if(auth()->user()->hasPermission('manage_businesses')): ?>
                        <a href="<?php echo e(route('business.relationships.create', $business)); ?>" 
                           class="mt-4 inline-block bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                            Add First Relationship
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Quick Stats -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-handshake text-blue-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Relationships</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($relationships->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Active</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($relationships->filter(fn($r) => $r->isCurrentlyActive())->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition duration-150 ease-in-out">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-users text-purple-500 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Partners</dt>
                                <dd class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($relationships->filter(fn($r) => $r->relationship_type === 'partner')->count()); ?></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH /Users/<USER>/Herd/business/plugins/business/Views/relationships/index.blade.php ENDPATH**/ ?>