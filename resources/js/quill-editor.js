import Quill from 'quill';
import 'quill/dist/quill.snow.css';

/**
 * Initialize Quill editor for todo description (backward compatibility)
 */
export function initializeQuillEditor() {
    return initializeQuillEditorWithConfig('todoDescriptionEditor', 'todoDescriptionHidden', 'Enter todo description (optional). You can use the formatting tools above.');
}

/**
 * Initialize Quill editor with custom configuration
 */
export function initializeQuillEditorWithConfig(editorId, hiddenInputId, placeholder = 'Enter text...', customToolbar = null) {
    const editorContainer = document.getElementById(editorId);
    const hiddenInput = document.getElementById(hiddenInputId);

    if (!editorContainer || !hiddenInput) {
        return null;
    }

    // Check if Quill is already initialized on this container
    if (editorContainer.__quill) {
        console.log(`Quill editor already initialized for ${editorId}`);
        return editorContainer.__quill;
    }

    // Use custom toolbar or default
    const toolbarOptions = customToolbar || [
        // Text formatting
        ['bold', 'italic', 'underline', 'strike'],

        // Font size and color
        [{ 'size': ['small', false, 'large', 'huge'] }],
        [{ 'color': [] }, { 'background': [] }],

        // Headers
        [{ 'header': [1, 2, 3, false] }],

        // Lists
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],

        // Text alignment
        [{ 'align': [] }],

        // Links and other elements
        ['link', 'blockquote', 'code-block'],

        // Clear formatting
        ['clean']
    ];

    // Initialize Quill with configuration
    const quill = new Quill(editorContainer, {
        theme: 'snow',
        placeholder: placeholder,
        modules: {
            toolbar: toolbarOptions,
            history: {
                delay: 1000,
                maxStack: 50,
                userOnly: true
            }
        }
    });

    // Store reference to prevent duplicate initialization
    editorContainer.__quill = quill;

    // Sync content with hidden input for form submission
    quill.on('text-change', function() {
        hiddenInput.value = quill.root.innerHTML;
    });

    // Handle keyboard shortcuts
    quill.keyboard.addBinding({
        key: 'B',
        ctrlKey: true
    }, function() {
        quill.format('bold', !quill.getFormat().bold);
    });

    quill.keyboard.addBinding({
        key: 'I',
        ctrlKey: true
    }, function() {
        quill.format('italic', !quill.getFormat().italic);
    });

    quill.keyboard.addBinding({
        key: 'U',
        ctrlKey: true
    }, function() {
        quill.format('underline', !quill.getFormat().underline);
    });

    return quill;
}

/**
 * Set content in the Quill editor
 */
export function setQuillContent(quill, content) {
    if (quill && content) {
        quill.root.innerHTML = content;
    }
}

/**
 * Clear content in the Quill editor
 */
export function clearQuillContent(quill) {
    if (quill) {
        quill.setContents([]);
    }
}

/**
 * Destroy a Quill editor instance
 */
export function destroyQuillEditor(editorId) {
    const editorContainer = document.getElementById(editorId);
    if (editorContainer && editorContainer.__quill) {
        try {
            const quill = editorContainer.__quill;
            // Remove event listeners
            quill.off('text-change');
            // Disable the editor
            if (typeof quill.disable === 'function') {
                quill.disable();
            }
            // Clear the container
            editorContainer.innerHTML = '';
            // Remove the reference
            delete editorContainer.__quill;
            console.log(`Quill editor destroyed for ${editorId}`);
            return true;
        } catch (error) {
            console.warn(`Error destroying Quill editor for ${editorId}:`, error);
            return false;
        }
    }
    return false;
}



/**
 * Get the HTML content from Quill editor
 */
export function getQuillContent(quill) {
    return quill ? quill.root.innerHTML : '';
}
