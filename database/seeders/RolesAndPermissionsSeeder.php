<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            ['name' => 'manage_users', 'display_name' => 'Manage Users', 'description' => 'Create, edit, and delete users'],
            ['name' => 'manage_roles', 'display_name' => 'Manage Roles', 'description' => 'Create, edit, and delete roles'],
            ['name' => 'manage_permissions', 'display_name' => 'Manage Permissions', 'description' => 'Assign and revoke permissions'],
            ['name' => 'manage_plugins', 'display_name' => 'Manage Plugins', 'description' => 'Enable, disable, and configure plugins'],
            ['name' => 'view_dashboard', 'display_name' => 'View Dashboard', 'description' => 'Access the main dashboard'],
            ['name' => 'manage_blog', 'display_name' => 'Manage Blog', 'description' => 'Create, edit, and delete blog posts'],
            ['name' => 'view_blog', 'display_name' => 'View Blog', 'description' => 'View blog posts'],

            // Business permissions
            ['name' => 'view_businesses', 'display_name' => 'View Businesses', 'description' => 'View business information, contacts, documents, and activities'],
            ['name' => 'manage_businesses', 'display_name' => 'Manage Businesses', 'description' => 'Create, edit, delete businesses and manage their contacts, documents, and activities'],
            ['name' => 'manage_business_users', 'display_name' => 'Manage Business Users', 'description' => 'Assign and remove users from businesses'],
            ['name' => 'view_business_reports', 'display_name' => 'View Business Reports', 'description' => 'Access business reports and analytics'],

            // Localization permissions
            ['name' => 'manage_localization', 'display_name' => 'Manage Localization', 'description' => 'Full access to localization management'],
            ['name' => 'view_localization', 'display_name' => 'View Localization', 'description' => 'View localization dashboard and statistics'],
            ['name' => 'manage_languages', 'display_name' => 'Manage Languages', 'description' => 'Create, edit, and delete languages'],
            ['name' => 'manage_translations', 'display_name' => 'Manage Translations', 'description' => 'Create, edit, and delete translations'],
            ['name' => 'export_translations', 'display_name' => 'Export Translations', 'description' => 'Export translation files'],
            ['name' => 'import_translations', 'display_name' => 'Import Translations', 'description' => 'Import translation files'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission['name']], $permission);
        }

        // Create roles
        $adminRole = Role::firstOrCreate(
            ['name' => 'admin'],
            [
                'display_name' => 'Administrator',
                'description' => 'Full system access with all permissions'
            ]
        );

        $editorRole = Role::firstOrCreate(
            ['name' => 'editor'],
            [
                'display_name' => 'Editor',
                'description' => 'Can manage content and view dashboard'
            ]
        );

        $userRole = Role::firstOrCreate(
            ['name' => 'user'],
            [
                'display_name' => 'User',
                'description' => 'Basic user with limited access'
            ]
        );

        // Assign permissions to roles
        $adminPermissions = Permission::all();
        $adminRole->permissions()->sync($adminPermissions->pluck('id'));

        $editorPermissions = Permission::whereIn('name', [
            'view_dashboard',
            'manage_blog',
            'view_blog',
            'view_localization',
            'manage_translations',
            'view_businesses',
            'manage_businesses'
        ])->get();
        $editorRole->permissions()->sync($editorPermissions->pluck('id'));

        $userPermissions = Permission::whereIn('name', [
            'view_dashboard',
            'view_blog',
            'view_businesses'
        ])->get();
        $userRole->permissions()->sync($userPermissions->pluck('id'));

        // Create default admin user
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'password' => Hash::make('password'),
                'role_id' => $adminRole->id,
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create a test editor user
        $editorUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Editor User',
                'password' => Hash::make('password'),
                'role_id' => $editorRole->id,
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create a test regular user
        $regularUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Regular User',
                'password' => Hash::make('password'),
                'role_id' => $userRole->id,
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );
    }
}
